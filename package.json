{"name": "pleros_tauri", "private": true, "version": "0.1.0", "type": "module", "packageManager": "pnpm@10.10.0", "scripts": {"dev:web": "turbo watch --filter @helios/web dev", "dev:app": "pnpm tauri dev", "dev:internal-tauri": "turbo watch --filter @helios/desktop dev", "build": "pnpm tauri build --no-bundle", "bundle:mac": "pnpm tauri bundle --bundles app,dmg", "tauri": "tauri", "prettier": "pnpm biome format --write apps/", "lint": "pnpm biome lint --write apps/", "check": "pnpm biome check --write apps/"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@tauri-apps/cli": "^2.4.1", "turbo": "^2.5.0"}}