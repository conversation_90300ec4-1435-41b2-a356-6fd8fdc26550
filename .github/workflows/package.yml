name: 'create packages'

on:
  workflow_dispatch:
  push:
   tags:
     - 'v*'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: 'macos-latest' # for Arm based macs (M1 and above).
            args: '--target aarch64-apple-darwin'
            target: arm64
         #  - platform: 'macos-latest' # for Intel based macs.
         #    args: '--target x86_64-apple-darwin'
         #    target: x86_64
          - platform: 'ubuntu-22.04'
            args: ''
            target: x86_64
          - platform: 'windows-latest'
            args: ''
            target: x86_64

    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v4

      - name: install dependencies (ubuntu only)
        if: matrix.platform == 'ubuntu-22.04' # This must match the platform value defined above.
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf

      - name: Install pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 10.10.0
          run_install: false

      - name: setup node
        uses: actions/setup-node@v4
        with:
         node-version: lts/*
         cache: "pnpm"

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: install Rust stable
        uses: dtolnay/rust-toolchain@stable # Set this to dtolnay/rust-toolchain@nightly
        with:
          # Those targets are only used on macos runners so it's in an `if` to slightly speed up windows and linux builds.
          targets: ${{ matrix.platform == 'macos-latest' && 'aarch64-apple-darwin,x86_64-apple-darwin' || '' }}

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: './crates/src-tauri -> target'

      - name: install frontend dependencies
        # The beforeBuildCommand is configured in tauri.conf.json
        run: pnpm install

      - uses: tauri-apps/tauri-action@v0.5.20
        id: tauri_build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          # This options are for create release
          # tagName: app-v__VERSION__ # the action automatically replaces \_\_VERSION\_\_ with the app version.
          # releaseName: 'Helios v__VERSION__'
          # releaseBody: 'See the assets to download this version and install.'
          releaseDraft: true
          prerelease: false
          args: ${{ matrix.args }}

      - uses: actions/upload-artifact@v4
        with:
         name: helios-${{matrix.platform}}-${{matrix.target}}-package
         path: ${{ steps.tauri_build.outputs.artifactPaths }}
         retention-days: 1