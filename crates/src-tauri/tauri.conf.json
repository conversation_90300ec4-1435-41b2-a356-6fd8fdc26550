{"$schema": "https://schema.tauri.app/config/2", "productName": "pleros_tauri", "version": "0.1.0", "identifier": "com.helios.app", "build": {"beforeDevCommand": "pnpm dev:internal-tauri", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm --filter @helios/desktop build", "frontendDist": "../../apps/desktop/dist"}, "app": {"macOSPrivateApi": true, "windows": [{"label": "main", "title": "<PERSON><PERSON><PERSON>", "maximized": true, "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "alwaysOnTop": false, "closable": true, "resizable": true, "dragDropEnabled": false, "decorations": true, "visible": false, "center": true}, {"label": "splashscreen", "url": "/splashscreen", "center": true, "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "visible": true}], "security": {"csp": "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' ws: wss:"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["schema/schema.json"], "macOS": {"signingIdentity": "-", "hardenedRuntime": true, "minimumSystemVersion": "10.13"}, "linux": {"deb": {"files": {"/usr/share/README.md": "../../README.md"}}}}}