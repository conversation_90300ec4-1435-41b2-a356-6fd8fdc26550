{"id": "pipeline_1", "name": "LempelZiv analysis", "description": "Pipeline for entropic analysis", "init": "read1", "nuts": {"lz_1": {"parameters": {"chunks": 10, "multiLine": true}, "breakpoint": false, "prev": "read1", "next": "nn_1", "inputType": ["collectionLiteral"], "outputType": ["object", "collectionObject"], "type": "operator", "data": {"label": "LempelZiv 76", "icon": "book"}}, "read1": {"parameters": {"format": "txt", "multiline": false, "path": "/path/to/data.txt"}, "type": "input", "next": ["lz_1"], "inputType": [], "outputType": ["collectionLiteral"]}, "nn_1": {"parameters": {"layers": [2, 3], "regularization": "L2", "optimizer": "adam"}, "type": "toolCustom", "inputType": ["collectionLiteral", "collectionObject"], "outputType": ["weights"], "next": ["out1", "doc1"], "prev": "lz_1", "data": {"label": "NeuralNetwork"}}, "out1": {"parameters": {"format": "weight", "path": "path/to/weight.cfg"}, "inputType": ["collectionLiteral", "collectionObject", "object", "literal"], "outputType": [], "prev": "nn_1", "type": "output"}, "doc1": {"parameters": {}, "inputType": ["collectionLiteral", "collectionObject", "object", "literal"], "outputType": ["document"], "prev": "nn_1", "type": "output"}}}