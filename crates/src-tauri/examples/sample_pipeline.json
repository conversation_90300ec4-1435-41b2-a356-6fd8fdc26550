{"id": "sample_pipeline_001", "name": "Sample Pipeline", "description": "A sample pipeline for testing the pipeline manager", "init": "start", "meta": {"created_at": "01/01/2023", "created_by": "System", "updated_at": "01/15/2023", "updated_by": "User", "tag": ["sample", "test", "demo"]}, "nuts": {"start": {"type": "input", "parameters": {"source": "file", "format": "json"}, "description": "Input node that reads data from a file", "next": "process", "inputType": ["Literal"], "outputType": ["Object"]}, "process": {"type": "transform", "parameters": {"operation": "filter", "condition": "value > 10"}, "description": "Transforms the data by filtering values", "prev": "start", "next": ["branch1", "branch2"], "inputType": ["Object"], "outputType": ["Object"]}, "branch1": {"type": "transform", "parameters": {"operation": "map", "mapping": "value * 2"}, "description": "First branch that doubles values", "prev": "process", "next": "merge", "inputType": ["Object"], "outputType": ["Object"]}, "branch2": {"type": "transform", "parameters": {"operation": "map", "mapping": "value + 5"}, "description": "Second branch that adds 5 to values", "prev": "process", "next": "merge", "inputType": ["Object"], "outputType": ["Object"]}, "merge": {"type": "operator", "parameters": {"strategy": "union"}, "description": "Merges the results from both branches", "prev": ["branch1", "branch2"], "next": "output", "inputType": ["Object", "Object"], "outputType": ["CollectionObject"]}, "output": {"type": "output", "parameters": {"destination": "file", "format": "json"}, "description": "Outputs the results to a file", "prev": "merge", "inputType": ["CollectionObject"], "outputType": ["Document"]}}}