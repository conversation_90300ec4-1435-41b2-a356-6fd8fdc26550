// use std::fs;

fn main() {
    // let manifest_dir = std::path::PathBuf::from(env!("CARGO_MANIFEST_DIR"));
    // println!("cargo:info=Manifest file {}", manifest_dir.display());
    // assert_eq!(manifest_dir.file_name().unwrap(), "src-tauri");

    // let schema_src = manifest_dir.join("schema/schema.json");
    // let schema_dst_dir = manifest_dir.join("target/schema");
    // let schema_dst = manifest_dir.join("target/schema/schema.json");

    // if !schema_dst_dir.exists() {
    //     fs::create_dir(schema_dst_dir).expect("Can't create the disrectory");
    // }

    // if !schema_dst.exists() {
    //     if schema_src.exists() {
    //         fs::copy(schema_src, schema_dst.clone())
    //             .expect(format!("cargo:warning=Schema file no copied to {}", schema_dst.display()).as_str());
    //     } else {
    //         println!("cargo:warning=Source file schema ({}) doesn't exist", schema_src.display());
    //     }
    // }

    // Run the default Tauri build process
    tauri_build::build();
}
