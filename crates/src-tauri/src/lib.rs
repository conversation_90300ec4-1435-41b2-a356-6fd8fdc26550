use std::{fs, path::PathBuf};
use std::time::Duration;
use std::sync::Mutex;
use tauri::async_runtime::spawn;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, State};
use tokio::time::sleep;

// Pipeline modules
mod pipeline;
mod schema_validator;
mod commands;
mod externals;
mod menu;

// Create a struct we'll use to track the completion of
// setup related tasks
struct SetupState {
    frontend_task: bool,
    backend_task: bool,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
fn list_dir(path: &str) -> Result<Vec<String>, String> {
    // Validate path is within allowed directories
    let path = PathBuf::from(path);
    // let doc_dir = document_dir().ok_or("Cannot access documents directory")?;
    // let download_dir = download_dir().ok_or("Cannot access downloads directory")?;

    // if !path.starts_with(&doc_dir) {
    //     return Err("Access denied: Path outside of allowed directories".into());
    // }

    // Read directory contents
    match fs::read_dir(&path) {
        Ok(dir) => Ok(dir
            .filter_map(|entry| {
                entry.ok()
                    .and_then(|e| e.file_name().into_string().ok())
            })
            .collect()),
        Err(e) => Err(format!("Failed to read directory: {}", e))
    }
}

#[tauri::command]
async fn set_complete(
    app: AppHandle,
    state: State<'_, Mutex<SetupState>>,
    task: String,
) -> Result<(), ()> {
    // Lock the state without write access
    let mut state_lock = state.lock().unwrap();
    match task.as_str() {
        "frontend" => state_lock.frontend_task = true,
        "backend" => state_lock.backend_task = true,
        _ => panic!("invalid task completed!"),
    }
    // Check if both tasks are completed
    if state_lock.backend_task && state_lock.frontend_task {
        // Setup is complete, we can close the splashscreen
        // and unhide the main window!
        let splash_window = app.get_webview_window("splashscreen").unwrap();
        let main_window = app.get_webview_window("main").unwrap();
        splash_window.close().unwrap();
        main_window.show().unwrap();
        
        #[cfg(debug_assertions)] // only include this code on debug builds
        {
            main_window.open_devtools();
        }

        app.on_menu_event(move |_handle, event| {
            menu::handle_event(&main_window.clone(), &event)
        });
    }

    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    #[cfg(debug_assertions)]
    let builder = tauri::Builder::default().plugin(tauri_plugin_devtools::init());
    #[cfg(not(debug_assertions))]
    let builder = tauri::Builder::default();

    builder
        .manage(Mutex::new(SetupState {
            frontend_task: false,
            backend_task: false,
        }))
        .setup(|app| {
            spawn(setup(app.handle().clone()));
            Ok(())
        })
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_notification::init())
        .menu(move |handle| menu::build(handle))
        .invoke_handler(tauri::generate_handler![
            greet,
            list_dir,
            set_complete,
            commands::validate_pipeline_file,
            commands::validate_pipeline_json,
            commands::build_pipeline_structure,
            commands::build_pipeline_structure_from_json,
            commands::save_pipeline,
            externals::lzcomplexity
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

// An async function that does some heavy setup task
async fn setup(app: AppHandle) -> Result<(), ()> {
    // Fake performing some heavy action for 3 seconds
    println!("Performing really heavy backend setup task...");
    sleep(Duration::from_secs(3)).await;
    println!("Backend setup task completed!");
    // Set the backend task as being completed
    // Commands can be ran as regular functions as long as you take
    // care of the input arguments yourself
    set_complete(
        app.clone(),
        app.state::<Mutex<SetupState>>(),
        "backend".to_string(),
    )
    .await?;
    Ok(())
}