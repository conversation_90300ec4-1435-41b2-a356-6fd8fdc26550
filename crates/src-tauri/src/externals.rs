use std::process::Command;

#[tauri::command]
pub fn lzcomplexity() -> Result<(), String> {
  let output = Command::new("/Users/<USER>/Documents/Work/Pleros_Helios_Platform/pleros_tauri/crates/src-tauri/externals/lzcomplexity")
        .args(["-v", "/Users/<USER>/Downloads/test.txt"])
        .output()
        .expect("Failed to execute process");
      
  println!("Exit status: {}", output.status);
  println!("Stdout: {}", String::from_utf8_lossy(&output.stdout));
  println!("Stderr: {}", String::from_utf8_lossy(&output.stderr));
  Ok(())
}