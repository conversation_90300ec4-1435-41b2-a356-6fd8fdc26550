use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::error::Error;
use std::fmt;

// Custom error type for pipeline operations
#[derive(Debug)]
pub enum PipelineError {
    IoError(std::io::Error),
    JsonError(serde_json::Error),
    ValidationError(String),
}

impl fmt::Display for PipelineError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            PipelineError::IoError(err) => write!(f, "I/O error: {}", err),
            PipelineError::JsonError(err) => write!(f, "JSON error: {}", err),
            PipelineError::ValidationError(msg) => write!(f, "Validation error: {}", msg),
        }
    }
}

impl Error for PipelineError {}

impl From<std::io::Error> for PipelineError {
    fn from(err: std::io::Error) -> Self {
        PipelineError::IoError(err)
    }
}

impl From<serde_json::Error> for PipelineError {
    fn from(err: serde_json::Error) -> Self {
        PipelineError::JsonError(err)
    }
}

// Date type that can handle different formats
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(untagged)]
pub enum Date {
    String(String),
}

// Data types as defined in the schema
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(untagged)]
pub enum DataType {
    Literal(String),
    Object(String),
    Graph(String),
    CollectionLiteral(String),
    CollectionObject(String),
    Document(String),
    Weight(String),
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Data {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub label: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub icon: Option<String>,
}

// Nut structure representing a node in the pipeline
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Nut {
    pub parameters: HashMap<String, serde_json::Value>,
    #[serde(rename = "type")]
    pub nut_type: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(default)]
    pub breakpoint: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub next: Option<NextPrev>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub prev: Option<NextPrev>,
    pub inputType: Vec<String>,
    pub outputType: Vec<String>,
    pub data: Option<Data>
}

// Next/Prev can be either a string or an array of strings
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(untagged)]
pub enum NextPrev {
    Single(String),
    Multiple(Vec<String>),
}

// Metadata for the pipeline
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Meta {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created_at: Option<Date>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created_by: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated_at: Option<Date>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated_by: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tag: Option<Vec<String>>,
}

// Main Pipeline structure
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Pipeline {
    pub id: String,
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    pub init: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub meta: Option<Meta>,
    pub nuts: HashMap<String, Nut>,
}

// Validation result structure
#[derive(Debug, Serialize, Deserialize)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub errors: Vec<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pipeline: Option<Pipeline>,
}

impl Pipeline {
    // Load a pipeline from a JSON file
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self, PipelineError> {
        let content = fs::read_to_string(path)?;
        let pipeline: Pipeline = serde_json::from_str(&content)?;
        Ok(pipeline)
    }

    // Load a pipeline from a JSON string
    pub fn from_json(json_str: &str) -> Result<Self, PipelineError> {
        let pipeline: Pipeline = serde_json::from_str(json_str)?;
        Ok(pipeline)
    }

    // Validate the pipeline structure
    pub fn validate(&self) -> ValidationResult {
        let mut errors = Vec::new();

        // Check if init nut exists
        if !self.nuts.contains_key(&self.init) {
            errors.push(format!("Init nut '{}' not found in nuts", self.init));
        }

        // Validate each nut's next/prev references
        for (nut_id, nut) in &self.nuts {
            // Check next references
            if let Some(next) = &nut.next {
                match next {
                    NextPrev::Single(next_id) => {
                        if !self.nuts.contains_key(next_id) {
                            errors.push(format!("Nut '{}' references non-existent next nut '{}'", nut_id, next_id));
                        }
                    },
                    NextPrev::Multiple(next_ids) => {
                        for next_id in next_ids {
                            if !self.nuts.contains_key(next_id) {
                                errors.push(format!("Nut '{}' references non-existent next nut '{}'", nut_id, next_id));
                            }
                        }
                    }
                }
            }

            // Check prev references
            if let Some(prev) = &nut.prev {
                match prev {
                    NextPrev::Single(prev_id) => {
                        if !self.nuts.contains_key(prev_id) {
                            errors.push(format!("Nut '{}' references non-existent prev nut '{}'", nut_id, prev_id));
                        }
                    },
                    NextPrev::Multiple(prev_ids) => {
                        for prev_id in prev_ids {
                            if !self.nuts.contains_key(prev_id) {
                                errors.push(format!("Nut '{}' references non-existent prev nut '{}'", nut_id, prev_id));
                            }
                        }
                    }
                }
            }
        }

        // Return validation result
        let is_empty = errors.is_empty();
        ValidationResult {
            is_valid: is_empty,
            errors: errors.clone(),
            pipeline: if is_empty { Some(self.clone()) } else { None },
        }
    }

    // Build a pipeline structure (this is a placeholder for actual implementation)
    pub fn build_structure(&self) -> Result<PipelineStructure, PipelineError> {
        let mut structure = PipelineStructure {
            nodes: Vec::new(),
            edges: Vec::new(),
        };

        // Add all nuts as nodes
        for (nut_id, nut) in &self.nuts {
            structure.nodes.push(PipelineNode {
                id: nut_id.clone(),
                nut_type: nut.nut_type.clone(),
                description: nut.description.clone(),
                input_types: nut.inputType.clone(),
                output_types: nut.outputType.clone(),
                data: nut.data.clone(),
            });
        }

        // Add edges based on next/prev relationships
        for (source_id, nut) in &self.nuts {
            if let Some(next) = &nut.next {
                match next {
                    NextPrev::Single(target_id) => {
                        structure.edges.push(PipelineEdge {
                            source: source_id.clone(),
                            target: target_id.clone(),
                        });
                    },
                    NextPrev::Multiple(target_ids) => {
                        for target_id in target_ids {
                            structure.edges.push(PipelineEdge {
                                source: source_id.clone(),
                                target: target_id.clone(),
                            });
                        }
                    }
                }
            }
        }

        Ok(structure)
    }
}

// Pipeline structure for visualization
#[derive(Debug, Serialize, Deserialize)]
pub struct PipelineStructure {
    pub nodes: Vec<PipelineNode>,
    pub edges: Vec<PipelineEdge>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PipelineNode {
    pub id: String,
    pub nut_type: String,
    pub description: Option<String>,
    pub input_types: Vec<String>,
    pub output_types: Vec<String>,
    pub data: Option<Data>
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PipelineEdge {
    pub source: String,
    pub target: String,
}
