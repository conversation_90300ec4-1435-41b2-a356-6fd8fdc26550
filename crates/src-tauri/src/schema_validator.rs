use std::fs;
use std::path::Path;
use serde_json::Value;
use crate::pipeline::{PipelineError, ValidationResult};

// Load the schema from a file
pub fn load_schema<P: AsRef<Path>>(path: P) -> Result<Value, PipelineError> {
    let schema_content = fs::read_to_string(path)?;
    let schema: Value = serde_json::from_str(&schema_content)?;
    Ok(schema)
}

// Validate JSON against the schema
pub fn validate_json(json_str: &str, schema: &Value) -> ValidationResult {
    // Parse the JSON
    let json_result: Result<Value, serde_json::Error> = serde_json::from_str(json_str);

    if let Err(err) = json_result {
        return ValidationResult {
            is_valid: false,
            errors: vec![format!("Invalid JSON: {}", err)],
            pipeline: None,
        };
    }

    let json_value = json_result.unwrap();

    // Use jsonschema crate to validate (this is a placeholder)
    // In a real implementation, you would use a JSON Schema validation library
    // For now, we'll do a basic check of required fields

    let mut errors = Vec::new();

    // Check required fields at the root level
    if let Some(required) = schema.get("required") {
        if let Some(required_array) = required.as_array() {
            for field in required_array {
                if let Some(field_name) = field.as_str() {
                    if !json_value.get(field_name).is_some() {
                        errors.push(format!("Missing required field: {}", field_name));
                    }
                }
            }
        }
    }

    // Check if nuts object exists and has at least one nut
    if let Some(nuts) = json_value.get("nuts") {
        if !nuts.is_object() || nuts.as_object().unwrap().is_empty() {
            errors.push("The 'nuts' object must contain at least one nut".to_string());
        }
    } else {
        errors.push("Missing required field: nuts".to_string());
    }

    // Check if init references a valid nut
    if let (Some(init), Some(nuts)) = (json_value.get("init"), json_value.get("nuts")) {
        if let (Some(init_str), Some(nuts_obj)) = (init.as_str(), nuts.as_object()) {
            if !nuts_obj.contains_key(init_str) {
                errors.push(format!("Init nut '{}' not found in nuts", init_str));
            }
        }
    }

    // Return validation result
    let is_empty = errors.is_empty();
    ValidationResult {
        is_valid: is_empty,
        errors: errors.clone(),
        pipeline: if is_empty {
            // If valid, try to parse into Pipeline struct
            match crate::pipeline::Pipeline::from_json(json_str) {
                Ok(pipeline) => Some(pipeline),
                Err(err) => {
                    errors.push(format!("Error parsing pipeline: {}", err));
                    None
                }
            }
        } else {
            None
        },
    }
}

// Helper function to validate a JSON file against the schema
pub fn validate_file<P: AsRef<Path>>(file_path: P, schema_path: P) -> Result<ValidationResult, PipelineError> {
    let schema = load_schema(schema_path)?;
    let json_content = fs::read_to_string(file_path)?;
    Ok(validate_json(&json_content, &schema))
}
