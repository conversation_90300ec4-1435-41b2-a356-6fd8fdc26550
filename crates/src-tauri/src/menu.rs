use anyhow::Context;
use tauri::menu::AboutMetadata;
use tauri::Emitter;
use tauri::{
   menu::{Menu, MenuEvent, MenuItemBuilder, Submenu, SubmenuBuilder, PredefinedMenuItem}, 
   AppHandle, Runtime, WebviewWindow
};

use tauri_plugin_dialog::DialogExt;
use tauri_plugin_fs::FilePath;

pub fn build<R: Runtime>(
   handle: &AppHandle<R>
) -> tauri::Result<tauri::menu::Menu<R>> {
   #[cfg(target_os = "macos")]
   let app_name = handle
       .config()
       .product_name
       .clone()
       .context("App name not defined.")?;

   #[cfg(target_os = "macos")]
   let settings_menu = MenuItemBuilder::with_id("global/settings", "Settings")
      .accelerator("CmdOrCtrl+,")
      .build(handle)?;

   #[cfg(target_os = "macos")]
   let mac_menu = &SubmenuBuilder::new(handle, app_name)
       .about(Some(AboutMetadata::default()))
       .separator()
       .item(&settings_menu)
      //  .item(&check_for_updates)
       .separator()
       .services()
       .separator()
       .hide()
       .hide_others()
       .show_all()
       .separator()
       .quit()
       .build()?;
   
   let file_menu = &SubmenuBuilder::new(handle, "File")
         .items(&[
            &MenuItemBuilder::with_id("file/load-pipeline", "Load Pipeline").accelerator("CmdOrCtrl+O").build(handle)?,
            &PredefinedMenuItem::separator(handle)?,
         ]).build()?;
   
   #[cfg(target_os = "macos")]
   file_menu.append(&PredefinedMenuItem::close_window(handle, None)?)?;

   #[cfg(not(target_os = "macos"))]
   file_menu.append_items(&[&PredefinedMenuItem::quit(handle, None)?])?;

   #[cfg(target_os = "macos")]
   let window_menu = &SubmenuBuilder::new(handle, "Window")
       .items(&[
           &PredefinedMenuItem::minimize(handle, None)?,
           &PredefinedMenuItem::maximize(handle, None)?,
           &PredefinedMenuItem::separator(handle)?,
           &PredefinedMenuItem::close_window(handle, None)?,
       ])
       .build()?;

   #[cfg(not(target_os = "linux"))]
   let edit_menu = &Submenu::new(handle, "Edit", true)?;

   #[cfg(target_os = "macos")]
   {
       edit_menu.append_items(&[
           &PredefinedMenuItem::undo(handle, None)?,
           &PredefinedMenuItem::redo(handle, None)?,
           &PredefinedMenuItem::separator(handle)?,
       ])?;
   }
   #[cfg(not(target_os = "linux"))]
   {
       edit_menu.append_items(&[
           &PredefinedMenuItem::cut(handle, None)?,
           &PredefinedMenuItem::copy(handle, None)?,
           &PredefinedMenuItem::paste(handle, None)?,
       ])?;
   }

   #[cfg(target_os = "macos")]
   edit_menu.append(&PredefinedMenuItem::select_all(handle, None)?)?;


   let view_menu = &Submenu::new(handle, "View", true)?;

   #[cfg(target_os = "macos")]
   view_menu.append(&PredefinedMenuItem::fullscreen(handle, None)?)?;

   Menu::with_items(
      handle,
      &[
          #[cfg(target_os = "macos")]
          mac_menu,
          file_menu,
          #[cfg(not(target_os = "linux"))]
          edit_menu,
          view_menu,
         //  project_menu,
          #[cfg(target_os = "macos")]
          window_menu,
         //  &help_menu,
      ],
  )
}

pub fn handle_event(webview: &WebviewWindow, event: &MenuEvent) {
   if event.id() == "global/settings" {
      let _ = webview.emit("helios/rust-event", "app-settings");
      return;
   }

   if event.id() == "file/load-pipeline" {
      let internal_webview = webview.clone();
      webview
         .dialog()
         .file()
         .add_filter("Json filter", &["json"])
         // .blocking_pick_file();
         .pick_file(move |path| { 
            if let Some(path_obj) = path {
               let path_str = match path_obj {
                   FilePath::Url(url) => url.to_file_path()
                       .ok()
                       .and_then(|p| p.to_str().map(String::from)),
                   FilePath::Path(path) => path.to_str().map(String::from),
               };
               
               if let Some(path_string) = path_str {
                  let _ = internal_webview.emit("helios/pipeline-file-loaded",path_string);
               }
            }
         });
      return;
   }
}