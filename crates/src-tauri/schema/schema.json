{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Helios platform pipeline definition", "type": "object", "definitions": {"date": {"anyOf": [{"type": "string", "description": "Format MM/DD/YYYY or MM-DD-YYYY or MM.DD.YYYY", "pattern": "^(0[1-9]|1[0-2])[/.-](0[1-9]|[12][0-9]|3[01])[/.-]\\d{4}$"}, {"type": "string", "description": "Format DD/MM/YYYY or DD-MM-YYYY or DD.MM.YYYY", "pattern": "^(0[1-9]|[12][0-9]|3[01])[/.-](0[1-9]|1[0-2])[/.-]\\d{4}$"}, {"type": "string", "description": "Format M/D/YYYY or M-D-YYYY or M.D.YYYY", "pattern": "^([1-9]|0[1-9]|1[0-2])[.-/][1-9]|(0[1-9]|[12][0-9]|3[01])[.-/]\\d{4}$"}]}, "literal": {"type": "string", "pattern": "[Ll][Ii][Tt][Ee][Rr][Aa][Ll]"}, "object": {"type": "string", "pattern": "[Oo][Bb][Jj][Ee][Cc][Tt]"}, "graph": {"type": "string", "pattern": "[Gg][Rr][Aa][Pp][Hh]"}, "collectionLiteral": {"type": "string", "pattern": "[Cc][Oo][Ll][Ll][Ee][Cc][Tt][Ii][Oo][Nn][Ll][Ii][Tt][Ee][Rr][Aa][Ll]"}, "collectionObject": {"type": "string", "pattern": "[Cc][Oo][Ll][Ll][Ee][Cc][Tt][Ii][Oo][Nn][Oo][Bb][Jj][Ee][Cc][Tt]"}, "document": {"type": "string", "pattern": "[Dd][Oo][Cc][Uu][Mm][Ee][Nn][Tt]"}, "weight": {"type": "string", "pattern": "[Ww][Ee][Ii][Gg][Hh][Tt][Ss]"}, "dataType": {"anyOf": [{"$ref": "#/definitions/literal"}, {"$ref": "#/definitions/object"}, {"$ref": "#/definitions/collectionLiteral"}, {"$ref": "#/definitions/collectionObject"}, {"$ref": "#/definitions/graph"}, {"$ref": "#/definitions/document"}, {"$ref": "#/definitions/weight"}]}, "nut": {"type": "object", "properties": {"parameters": {"type": "object", "properties": {}, "additionalProperties": true}, "type": {"type": "string"}, "description": {"type": "string"}, "breakpoint": {"type": "boolean", "default": false}, "next": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "prev": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "inputType": {"type": "array", "items": {"$ref": "#/definitions/dataType"}}, "outputType": {"type": "array", "items": {"$ref": "#/definitions/dataType"}}, "data": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "null"}]}}, "required": ["type", "parameters", "inputType", "outputType"]}}, "properties": {"id": {"type": "string", "description": "ID of the pipeline for Helios platform"}, "name": {"type": "string", "description": "Name of the pipeline for Helios platform"}, "description": {"type": "string", "description": "Description of the pipeline for Helios platform"}, "init": {"type": "string"}, "meta": {"type": "object", "properties": {"created_at": {"$ref": "#/definitions/date"}, "created_by": {"type": "string"}, "updated_at": {"$ref": "#/definitions/date"}, "updated_by": {"type": "string"}, "tag": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, "nuts": {"type": "object", "patternProperties": {"[A-Za-z0-9_]{1,32}$": {"$ref": "#/definitions/nut"}}, "additionalProperties": false}}, "required": ["id", "name", "init", "nuts"]}