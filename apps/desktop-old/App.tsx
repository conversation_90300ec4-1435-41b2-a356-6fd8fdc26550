import { useEffect, useState } from "preact/hooks";
import { signal } from "@preact/signals";
import preactLogo from "./assets/preact.svg";
import { invoke } from "@tauri-apps/api/core";
import "./App.css";
import { lazy, Suspense } from "preact/compat";
import PipelinePage from "./pages/PipelinePage";

import { getCurrentWindow } from "@tauri-apps/api/window";

// Control the current window
const appWindow = getCurrentWindow();

async function list_dir_cmp() {
	const list: Array<string> = await invoke("list_dir", {
		path: "/Users/<USER>/Documents",
	});

	console.log({ list });
	return (
		<ul>
			{list.map((elem) => (
				<li key={elem}>{elem}</li>
			))}
		</ul>
	);
}

function App() {
	const [greetMsg, setGreetMsg] = useState("");
	const name = signal("");

	const [list, setList] = useState<Array<string>>([]);

	async function greet() {
		// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
		setGreetMsg(await invoke("greet", { name }));
	}

	async function list_dir() {
		try {
			const list: Array<string> = await invoke("list_dir", {
				path: "/Users/<USER>/Documents",
			});
			setList(list);
		} catch (error) {
			console.error("Failed to list directory:", error);
			// Handle error appropriately in your UI
		}
	}

	const ListDir = lazy(list_dir_cmp);

	const [showPipeline, setShowPipeline] = useState(false);

	// useEffect(() => {
	// 	document.getElementById("titlebar")?.addEventListener("mousedown", (e) => {
	// 		if (e.buttons === 1) {
	// 			// Primary (left) button
	// 			e.detail === 2
	// 				? appWindow.toggleMaximize() // Maximize on double click
	// 				: appWindow.startDragging(); // Else start dragging
	// 		}
	// 	});
	// }, []);

	return (
		<main class="container">
			{/* <header
				data-tauri-drag-region
				class="bg-red-500 h-7 w-full flex justify-end"
			>
				<button class="h-full" type="button" onClick={() => appWindow.close()}>
					Close
				</button>
				<button
					class="h-full"
					type="button"
					onClick={() => appWindow.toggleMaximize()}
				>
					Max
				</button>
			</header> */}
			<div class="w-full h-20 bg-red-400 select-none " data-tauri-drag-region>
				<span class="bg-green-500 w-fit pointer-events-none">Drag region</span>
			</div>
			{showPipeline ? (
				<div>
					<button type="button" onClick={() => setShowPipeline(false)}>
						Back to Home
					</button>
					<PipelinePage />
				</div>
			) : (
				<div>
					<h1>Welcome to Tauri + Preact</h1>

					<div class="row">
						<a href="https://vitejs.dev" target="_blank" rel="noreferrer">
							<img src="/assets/vite.svg" class="logo vite" alt="Vite logo" />
						</a>
						<a href="https://tauri.app" target="_blank" rel="noreferrer">
							<img
								src="/assets/tauri.svg"
								class="logo tauri"
								alt="Tauri logo"
							/>
						</a>
						<a href="https://preactjs.com" target="_blank" rel="noreferrer">
							<img src={preactLogo} class="logo preact" alt="Preact logo" />
						</a>
					</div>
					<p>Click on the Tauri, Vite, and Preact logos to learn more.</p>

					<button type="button" onClick={() => setShowPipeline(true)}>
						Open Pipeline Manager
					</button>

					<Suspense
						fallback={
							<div>
								<p>Waiting...</p>
							</div>
						}
					>
						{ListDir}
					</Suspense>
					<form
						class="row mt-5 h-10"
						onSubmit={(e) => {
							e.preventDefault();
							greet();
							list_dir();
						}}
					>
						<input
							id="greet-input"
							onInput={(e) => {
								name.value = e.currentTarget.value;
							}}
							placeholder="Enter a name..."
							class="px-4 rounded-sm"
						/>
						<button type="submit">Greet</button>
					</form>
					<p>{greetMsg}</p>
				</div>
			)}
		</main>
	);
}

export default App;
