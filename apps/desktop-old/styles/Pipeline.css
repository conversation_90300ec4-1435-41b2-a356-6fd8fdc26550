.pipeline-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.pipeline-uploader {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background-color: #f9f9f9;
}

.upload-section {
  margin-bottom: 20px;
}

.file-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #eee;
  border-radius: 4px;
}

.file-info button {
  margin-right: 10px;
  margin-top: 10px;
}

.error-message {
  color: #d32f2f;
  padding: 10px;
  background-color: #ffebee;
  border-radius: 4px;
  margin-top: 10px;
}

.validation-results {
  margin-top: 20px;
}

.validation-results .valid {
  color: #2e7d32;
  font-weight: bold;
}

.validation-results .invalid {
  color: #d32f2f;
}

.error-list {
  margin-top: 10px;
  padding-left: 20px;
}

.structure-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #e8f5e9;
  border-radius: 4px;
}

.pipeline-visualizer {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background-color: #fff;
}

.node-list {
  margin-top: 20px;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.node-list ul {
  list-style-type: none;
  padding: 0;
}

.node-list li {
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.node-list li:last-child {
  border-bottom: none;
}

button {
  padding: 8px 16px;
  background-color: #4a86e8;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

button:hover {
  background-color: #3a76d8;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}
