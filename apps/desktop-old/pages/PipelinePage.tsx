import { useState } from "preact/hooks";
import PipelineUploader from "../components/PipelineUploader";
import PipelineVisualizer from "../components/PipelineVisualizer";
import "../styles/Pipeline.css";

interface ValidationResult {
	is_valid: boolean;
	errors: string[];
	pipeline?: any;
}

interface PipelineNode {
	id: string;
	nut_type: string;
	description?: string;
	input_types: string[];
	output_types: string[];
}

interface PipelineEdge {
	source: string;
	target: string;
}

interface PipelineStructure {
	nodes: PipelineNode[];
	edges: PipelineEdge[];
}

export default function PipelinePage() {
	const [validationResult, setValidationResult] =
		useState<ValidationResult | null>(null);
	const [pipelineStructure, setPipelineStructure] =
		useState<PipelineStructure | null>(null);

	const handleValidationComplete = (result: ValidationResult) => {
		setValidationResult(result);
	};

	const handleStructureBuilt = (structure: PipelineStructure) => {
		setPipelineStructure(structure);
	};
	return (
		<div className="pipeline-page">
			<h1 class="text-red-500 text-3xl">Pipeline Manager</h1>

			<PipelineUploader
				onValidationComplete={handleValidationComplete}
				onStructureBuilt={handleStructureBuilt}
			/>

			{pipelineStructure && (
				<PipelineVisualizer structure={pipelineStructure} />
			)}
		</div>
	);
}
