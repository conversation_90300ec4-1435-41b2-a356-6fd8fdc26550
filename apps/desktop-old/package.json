{"name": "@helios/desktop-old", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev-old": "vite", "build-old": "vite build", "preview-old": "vite preview", "tauri-old": "tauri", "check-types-old": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.2", "@preact/signals": "catalog:preact", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "^2.0.0", "@tauri-apps/plugin-fs": "^2.0.0", "@tauri-apps/plugin-opener": "^2", "lucide-preact": "^0.488.0", "preact": "catalog:preact"}, "devDependencies": {"@preact/preset-vite": "catalog:preact", "@tailwindcss/vite": "^4.1.4", "@tauri-apps/cli": "^2", "tailwindcss": "^4.1.4", "typescript": "~5.6.2", "vite": "catalog:"}}