import { h, Component } from "preact";

class DndListItem extends Component {
	render({ onDragStart, children }) {
		return (
			<div
				class="dnd-list-item"
				draggable
				onDragStart={onDragStart}
				style={{
					padding: "10px",
					border: "1px solid #ccc",
					marginBottom: "5px",
					background: "#fff",
				}}
			>
				{children}
			</div>
		);
	}
}

class DndList extends Component {
	onDragStart = (index, ev) => {
		ev.dataTransfer.setData("text", index);
	};

	hit(r, p) {
		return (
			p.x >= r.x && p.x < r.x + r.width && p.y >= r.y && p.y < r.y + r.height
		);
	}

	onDrop = (ev) => {
		ev.preventDefault();
		let draggedIdx = parseInt(ev.dataTransfer.getData("text"));
		let pos = { x: ev.clientX, y: ev.clientY };
		let children = Array.from(this.base.querySelectorAll(".dnd-list-item"));
		let insertBeforeIdx = null;
		for (let idx = 0, n = children.length; idx < n; ++idx) {
			let childRect = children[idx].getBoundingClientRect();
			if (this.hit(childRect, pos)) {
				this.props.onMoveItem(draggedIdx, idx);
				return;
			}
			if (insertBeforeIdx == null && childRect.y >= pos.y) {
				insertBeforeIdx = idx;
			}
		}
		this.props.onMoveItem(draggedIdx, insertBeforeIdx);
	};

	render({ children }) {
		return (
			<div
				class="dnd-list"
				onDrop={this.onDrop}
				onDragOver={(ev) => ev.preventDefault()}
			>
				{children.map((it, index) => (
					<DndListItem
						key={index}
						onDragStart={(...args) => this.onDragStart(index, ...args)}
					>
						{it}
					</DndListItem>
				))}
			</div>
		);
	}
}

export default class Drag extends Component {
	state = {
		items: [
			{ title: "First Item" },
			{ title: "Second Item" },
			{ title: "Third Item" },
			{ title: "Fourth Item" },
			{ title: "Fifth Item" },
		],
	};

	onMoveItem = (source, dest) => {
		let { items } = this.state;
		let sourceItem = items.splice(source, 1);
		items.splice(dest, 0, ...sourceItem);
		this.setState({ items });
	};

	render({}, { items }) {
		return (
			<div style="width:300px;margin:0 auto">
				<DndList onMoveItem={this.onMoveItem}>
					{items.map((it) => (
						<div key={it.title}>{it.title}</div>
					))}
				</DndList>
			</div>
		);
	}
}
