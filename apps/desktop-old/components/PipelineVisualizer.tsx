import { useEffect, useRef } from "preact/hooks";

interface PipelineNode {
	id: string;
	nut_type: string;
	description?: string;
	input_types: string[];
	output_types: string[];
}

interface PipelineEdge {
	source: string;
	target: string;
}

interface PipelineStructure {
	nodes: PipelineNode[];
	edges: PipelineEdge[];
}

interface PipelineVisualizerProps {
	structure: PipelineStructure;
}

export default function PipelineVisualizer({
	structure,
}: PipelineVisualizerProps) {
	const canvasRef = useRef<HTMLCanvasElement>(null);

	useEffect(() => {
		if (!canvasRef.current || !structure) return;

		const canvas = canvasRef.current;
		const ctx = canvas.getContext("2d");
		if (!ctx) return;

		// Set canvas size
		canvas.width = 800;
		canvas.height = 600;

		// Clear canvas
		ctx.clearRect(0, 0, canvas.width, canvas.height);

		// Calculate node positions (simple layout)
		const nodePositions = calculateNodePositions(
			structure.nodes,
			canvas.width,
			canvas.height,
		);

		// Draw edges
		drawEdges(ctx, structure.edges, nodePositions);

		// Draw nodes
		drawNodes(ctx, structure.nodes, nodePositions);
	}, [structure]);

	// Simple layout algorithm to position nodes
	const calculateNodePositions = (
		nodes: PipelineNode[],
		width: number,
		height: number,
	) => {
		const positions: Record<string, { x: number; y: number }> = {};

		// Very simple layout - just place nodes in a grid
		const nodeCount = nodes.length;
		const cols = Math.ceil(Math.sqrt(nodeCount));
		const rows = Math.ceil(nodeCount / cols);

		const cellWidth = width / (cols + 1);
		const cellHeight = height / (rows + 1);

		nodes.forEach((node, index) => {
			const row = Math.floor(index / cols);
			const col = index % cols;

			positions[node.id] = {
				x: (col + 1) * cellWidth,
				y: (row + 1) * cellHeight,
			};
		});

		return positions;
	};

	// Draw edges between nodes
	const drawEdges = (
		ctx: CanvasRenderingContext2D,
		edges: PipelineEdge[],
		nodePositions: Record<string, { x: number; y: number }>,
	) => {
		ctx.strokeStyle = "#666";
		ctx.lineWidth = 3;

		edges.forEach((edge) => {
			const sourcePos = nodePositions[edge.source];
			const targetPos = nodePositions[edge.target];
			const r = 40;

			const dx = targetPos.x - sourcePos.x;
			const dy = targetPos.y - sourcePos.y;
			const distance = Math.sqrt(dx * dx + dy * dy);
			const dirX = dx / distance;
			const dirY = dy / distance;

			// Adjust points to circle edges
			const startX = sourcePos.x + dirX * r;
			const startY = sourcePos.y + dirY * r;
			const endX = targetPos.x - dirX * r;
			const endY = targetPos.y - dirY * r;

			if (sourcePos && targetPos) {
				ctx.beginPath();
				ctx.moveTo(startX, startY);
				ctx.lineTo(endX, endY);
				ctx.stroke();

				// Draw arrow at the end
				const angle = Math.atan2(dy, dx);
				const arrowSize = 20;

				ctx.beginPath();
				ctx.moveTo(endX, endY);
				ctx.lineTo(
					endX - arrowSize * Math.cos(angle - Math.PI / 6),
					endY - arrowSize * Math.sin(angle - Math.PI / 6),
				);
				ctx.lineTo(
					endX - arrowSize * Math.cos(angle + Math.PI / 6),
					endY - arrowSize * Math.sin(angle + Math.PI / 6),
				);
				ctx.closePath();
				ctx.fillStyle = "#666";
				ctx.fill();
			}
		});
	};

	// Draw nodes
	const drawNodes = (
		ctx: CanvasRenderingContext2D,
		nodes: PipelineNode[],
		nodePositions: Record<string, { x: number; y: number }>,
	) => {
		const nodeRadius = 40;

		nodes.forEach((node) => {
			const pos = nodePositions[node.id];
			if (!pos) return;

			// Draw node circle
			ctx.beginPath();
			ctx.arc(pos.x, pos.y, nodeRadius, 0, Math.PI * 2);
			ctx.fillStyle = "#4a86e8";
			ctx.fill();
			ctx.strokeStyle = "#2a5db0";
			ctx.lineWidth = 2;
			ctx.stroke();

			// Draw node text
			ctx.fillStyle = "#000";
			ctx.font = "14px Arial";
			ctx.textAlign = "center";
			ctx.textRendering = "optimizeLegibility";
			ctx.textBaseline = "middle";
			ctx.fillText(node.id, pos.x, pos.y);

			// Draw node type below
			ctx.font = "12px Arial";
			ctx.textRendering = "optimizeLegibility";
			ctx.fillText(node.nut_type, pos.x, pos.y + nodeRadius + 15);
		});
	};

	return (
		<div className="pipeline-visualizer">
			<h2>Pipeline Visualization</h2>
			<canvas
				ref={canvasRef}
				style={{ border: "1px solid #ccc", maxWidth: "100%" }}
			/>
			<div className="node-list">
				<h3>Nodes ({structure.nodes.length})</h3>
				<ul>
					{structure.nodes.map((node) => (
						<li key={node.id}>
							<strong>{node.id}</strong> ({node.nut_type})
							{node.description && <p>{node.description}</p>}
						</li>
					))}
				</ul>
			</div>
		</div>
	);
}
