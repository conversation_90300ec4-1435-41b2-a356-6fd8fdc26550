import { useState } from "preact/hooks";
import { invoke } from "@tauri-apps/api/core";
import { open } from "@tauri-apps/plugin-dialog";
import { readTextFile } from "@tauri-apps/plugin-fs";

interface ValidationResult {
	is_valid: boolean;
	errors: string[];
	pipeline?: any;
}

interface PipelineStructure {
	nodes: Array<{
		id: string;
		nut_type: string;
		description?: string;
		input_types: string[];
		output_types: string[];
	}>;
	edges: Array<{
		source: string;
		target: string;
	}>;
}

interface PipelineUploaderProps {
	onValidationComplete?: (result: ValidationResult) => void;
	onStructureBuilt?: (structure: PipelineStructure) => void;
}

export default function PipelineUploader({
	onValidationComplete,
	onStructureBuilt,
}: PipelineUploaderProps) {
	const [file, setFile] = useState<string | null>(null);
	const [fileName, setFileName] = useState<string>("");
	const [validationResult, setValidationResult] =
		useState<ValidationResult | null>(null);
	const [pipelineStructure, setPipelineStructure] =
		useState<PipelineStructure | null>(null);
	const [loading, setLoading] = useState<boolean>(false);
	const [error, setError] = useState<string | null>(null);

	const handleFileSelect = async () => {
		try {
			// Open file dialog
			const selected = await open({
				multiple: false,
				filters: [{ name: "JSON", extensions: ["json"] }],
			});

			if (selected && !Array.isArray(selected)) {
				setFileName(selected);

				// Read file content
				const content = await readTextFile(selected);
				setFile(content);

				// Reset states
				setValidationResult(null);
				setPipelineStructure(null);
				setError(null);
			}
		} catch (err) {
			setError(`Error selecting file: ${err}`);
		}
	};

	const validatePipeline = async () => {
		if (!file) {
			setError("No file selected");
			return;
		}

		setLoading(true);
		setError(null);

		try {
			// Validate the pipeline JSON
			const result: ValidationResult = await invoke("validate_pipeline_json", {
				jsonStr: file,
			});
			setValidationResult(result);

			if (onValidationComplete) {
				onValidationComplete(result);
			}
			console.log({ result });

			// If valid, build the pipeline structure
			if (result.is_valid) {
				const structure: PipelineStructure = await invoke(
					"build_pipeline_structure_from_json",
					{ jsonStr: file },
				);
				setPipelineStructure(structure);
				console.log({ structure });

				if (onStructureBuilt) {
					onStructureBuilt(structure);
				}
			}
		} catch (err) {
			setError(`Error validating pipeline: ${err}`);
		} finally {
			setLoading(false);
		}
	};

	const validateFile = async () => {
		if (!fileName) {
			setError("No file selected");
			return;
		}

		setLoading(true);
		setError(null);

		try {
			// Validate the pipeline file
			const result: ValidationResult = await invoke("validate_pipeline_file", {
				filePath: fileName,
			});
			setValidationResult(result);

			if (onValidationComplete) {
				onValidationComplete(result);
			}

			// If valid, build the pipeline structure
			if (result.is_valid) {
				const structure: PipelineStructure = await invoke(
					"build_pipeline_structure",
					{ filePath: fileName },
				);
				setPipelineStructure(structure);

				if (onStructureBuilt) {
					onStructureBuilt(structure);
				}
			}
		} catch (err) {
			setError(`Error validating pipeline file: ${err}`);
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="pipeline-uploader">
			<h2>Pipeline Uploader</h2>

			<div className="upload-section">
				<button type="button" onClick={handleFileSelect} disabled={loading}>
					Select Pipeline JSON File
				</button>

				{fileName && (
					<div className="file-info">
						<p>Selected file: {fileName}</p>
						<button type="button" onClick={validateFile} disabled={loading}>
							Validate File
						</button>
						<button
							type="button"
							onClick={validatePipeline}
							disabled={loading || !file}
						>
							Validate JSON Content
						</button>
					</div>
				)}

				{loading && <p>Loading...</p>}

				{error && (
					<div className="text-red-600 p-2.5 bg-red-100 rounded-sm mt-2.5">
						<p>Error: {error}</p>
					</div>
				)}
			</div>

			{validationResult && (
				<div className="validation-results">
					<h3>Validation Results</h3>
					{validationResult.is_valid ? (
						<p className="valid">Pipeline is valid! ✅</p>
					) : (
						<div className="invalid">
							<p>Pipeline is invalid! ❌</p>
							<ul className="error-list">
								{validationResult.errors.map((error, index) => (
									<li key={index}>{error}</li>
								))}
							</ul>
						</div>
					)}
				</div>
			)}

			{pipelineStructure && (
				<div className="structure-summary">
					<h3>Pipeline Structure</h3>
					<p>Nodes: {pipelineStructure.nodes.length}</p>
					<p>Edges: {pipelineStructure.edges.length}</p>
				</div>
			)}
		</div>
	);
}
