{"name": "@helios/desktop", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri", "check-types": "tsc --noEmit"}, "dependencies": {"@dagrejs/dagre": "^1.1.4", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.2", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "^2.0.0", "@tauri-apps/plugin-fs": "^2.0.0", "@tauri-apps/plugin-notification": "^2.2.2", "@tauri-apps/plugin-opener": "^2.0.0", "@xyflow/react": "^12.6.0", "class-variance-authority": "^0.7.1", "lucide-preact": "^0.488.0", "lucide-react": "^0.507.0", "react": "catalog:react", "react-dom": "catalog:react", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/vite": "^4.1.4", "@tauri-apps/cli": "^2", "@types/node": "^22.14.1", "@types/react": "catalog:react", "@types/react-dom": "catalog:react", "@vitejs/plugin-react-swc": "^3.8.0", "globals": "^16.0.0", "tailwindcss": "^4.1.4", "typescript": "~5.7.2", "vite": "catalog:"}}