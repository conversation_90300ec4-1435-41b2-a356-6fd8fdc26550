import * as React from "react";
import type { NodeType } from "../types/pipeline.types";

const DnDContext = React.createContext<
	[NodeType | null, React.Dispatch<React.SetStateAction<NodeType | null>>]
>([null, (_) => {}]);

export const DnDProvider: React.FC<any> = ({ children }) => {
	const [type, setType] = React.useState<NodeType | null>(null);

	return (
		<DnDContext.Provider value={[type, setType]}>
			{children}
		</DnDContext.Provider>
	);
};

export default DnDContext;

export const useDnD = () => {
	return React.useContext(DnDContext);
};
