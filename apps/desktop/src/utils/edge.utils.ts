import {
	getBezierPath,
	getSmoothStepPath,
	Position,
	type GetBezierPathParams,
} from "@xyflow/react";
import type { EdgeData } from "../types/pipeline.types";

const EDGE_PADDING_BOTTOM = 130;
const EDGE_PADDING_X = 40;
const EDGE_BORDER_RADIUS = 16;
const HANDLE_SIZE = 20; // Required to avoid connection line glitching when initially interacting with the handle

const isRightOfSourceHandle = (sourceX: number, targetX: number) =>
	sourceX - HANDLE_SIZE > targetX;

export function getEdgeRenderData(
	props: Pick<
		GetBezierPathParams,
		| "sourceX"
		| "sourceY"
		| "sourcePosition"
		| "targetX"
		| "targetY"
		| "targetPosition"
	>,
) {
	const { targetX, targetY, sourceX, sourceY, sourcePosition, targetPosition } =
		props;
	const isConnectorStraight = sourceY === targetY;

	if (!isRightOfSourceHandle(sourceX, targetX)) {
		const segment = getBezierPath(props);
		return {
			segments: [segment],
			labelPosition: [segment[1], segment[2]],
			isConnectorStraight,
		};
	}

	// Connection is backwards and the source is on the right side
	// -> We need to avoid overlapping the source node
	const firstSegmentTargetX = (sourceX + targetX) / 2;
	const firstSegmentTargetY = sourceY + EDGE_PADDING_BOTTOM;
	const firstSegment = getSmoothStepPath({
		sourceX,
		sourceY,
		targetX: firstSegmentTargetX,
		targetY: firstSegmentTargetY,
		sourcePosition,
		targetPosition: Position.Right,
		borderRadius: EDGE_BORDER_RADIUS,
		offset: EDGE_PADDING_X,
	});

	const secondSegment = getSmoothStepPath({
		sourceX: firstSegmentTargetX,
		sourceY: firstSegmentTargetY,
		targetX,
		targetY,
		sourcePosition: Position.Left,
		targetPosition,
		borderRadius: EDGE_BORDER_RADIUS,
		offset: EDGE_PADDING_X,
	});

	return {
		segments: [firstSegment, secondSegment],
		labelPosition: [firstSegmentTargetX, firstSegmentTargetY],
		isConnectorStraight,
	};
}

/**
 * Create a new edge with default properties
 */
export function createEdge(
	source: string,
	target: string,
	data: Partial<EdgeData> = {},
) {
	return {
		id: `edge-${source}-${target}`,
		source,
		target,
		type: "custom-edge",
		data: {
			label: data.label,
			status: data.status,
			isRunning: data.isRunning,
			...data,
		},
	};
}

/**
 * Check if an edge would create a cycle in the graph
 */
export function wouldCreateCycle(
	source: string,
	target: string,
	edges: any[],
	nodes: any[],
) {
	// If source and target are the same, it's a self-loop
	if (source === target) {
		return true;
	}

	// Create an adjacency list from the edges
	const adjacencyList: Record<string, string[]> = {};

	// Initialize the adjacency list with all nodes
	nodes.forEach((node) => {
		adjacencyList[node.id] = [];
	});

	// Add existing edges to the adjacency list
	edges.forEach((edge) => {
		if (!adjacencyList[edge.source]) {
			adjacencyList[edge.source] = [];
		}
		adjacencyList[edge.source].push(edge.target);
	});

	// Add the new edge
	if (!adjacencyList[source]) {
		adjacencyList[source] = [];
	}
	adjacencyList[source].push(target);

	// Check for cycles using DFS
	const visited = new Set<string>();
	const recursionStack = new Set<string>();

	function hasCycle(node: string): boolean {
		// Mark the current node as visited and add to recursion stack
		visited.add(node);
		recursionStack.add(node);

		// Visit all the neighbors
		const neighbors = adjacencyList[node] || [];
		for (const neighbor of neighbors) {
			// If the neighbor is not visited, recursively check for cycles
			if (!visited.has(neighbor)) {
				if (hasCycle(neighbor)) {
					return true;
				}
			}
			// If the neighbor is in the recursion stack, there's a cycle
			else if (recursionStack.has(neighbor)) {
				return true;
			}
		}

		// Remove the node from recursion stack
		recursionStack.delete(node);
		return false;
	}

	// Check for cycles starting from each unvisited node
	for (const node of Object.keys(adjacencyList)) {
		if (!visited.has(node)) {
			if (hasCycle(node)) {
				return true;
			}
		}
	}

	return false;
}
