@import url(./react_flow_custom.css);

#root {
	margin: 0;
	text-align: center;
	overflow: hidden;
}

.logo {
	height: 6em;
	padding: 1.5em;
	will-change: filter;
	transition: filter 300ms;
}
.logo:hover {
	filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
	filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.edge {
	transition: stroke 0.3s ease, fill 0.3s ease;
}

@media (prefers-reduced-motion: no-preference) {
	a:nth-of-type(2) .logo {
		animation: logo-spin infinite 20s linear;
	}
}

.card {
	padding: 2em;
}

.read-the-docs {
	color: #888;
}

.button-edge__button {
	display: grid;
	place-content: center;
	width: 30px;
	height: 30px;
	border: 5px solid #d3d2e5;
	color: var(--xy-edge-label-color-default);
	background-color: #f3f3f4;
	cursor: pointer;
	border-radius: 50%;
	font-size: 12px;
	padding: 12px;
}

.button-edge__button:hover {
	border: 5px solid #d3d2e5;
	background-color: var(--xy-theme-hover);
}

.button-edge__label {
	position: absolute;
	pointer-events: all;
	transform-origin: center;
}

@keyframes flowAnimation {
	0% {
		stroke-dashoffset: 100;
	}
	100% {
		stroke-dashoffset: 0;
	}
}

.button-edge__label {
	position: absolute;
	pointer-events: all;
	transform-origin: center;
}

a {
	font-weight: 500;
	color: #646cff;
	text-decoration: inherit;
}
a:hover {
	color: #535bf2;
}

body {
	margin: 0;
	display: flex;
	place-items: center;
	min-width: 320px;
	min-height: 100vh;
}

h1 {
	font-size: 3.2em;
	line-height: 1.1;
}
