@import "tailwindcss";

/* @config "../../tailwind.config.js"; */

/* it is for config dark theme manually */
@custom-variant dark (&:where(.dark, .dark *));

@theme inline {
	--color-main-background: var(--main-background);
	--color-background: hsl(var(--background));
	--color-foreground: hsl(var(--foreground));
	--color-card: hsl(var(--card));
	--color-card-foreground: hsl(var(--card-foreground));
	--color-popover: hsl(var(--popover));
	--color-popover-foreground: hsl(var(--popover-foreground));
	--color-primary: hsl(var(--primary));
	--color-primary-foreground: hsl(var(--primary-foreground));
	--color-secondary: hsl(var(--secondary));
	--color-secondary-foreground: hsl(var(--secondary-foreground));
	--color-muted: hsl(var(--muted));
	--color-muted-foreground: hsl(var(--muted-foreground));
	--color-accent: hsl(var(--accent));
	--color-accent-foreground: hsl(var(--accent-foreground));
	--color-destructive: hsl(var(--destructive));
	--color-destructive-foreground: hsl(var(--destructive-foreground));
	--color-border: hsl(var(--border));
	--color-input: hsl(var(--input));
	--color-ring: hsl(var(--ring));
	--radius-radius: 0.5rem;
}

:root {
	font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
	line-height: 1.5;
	font-weight: 400;

	color-scheme: light dark;
	color: rgba(255, 255, 255, 0.87);

	font-synthesis: none;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;

	--main-background: #d3d2e5;
	--background: 0 0% 100%;
	--foreground: 240 10% 3.9%;
	--card: 0 0% 100%;
	--card-foreground: 240 10% 3.9%;
	--popover: 0 0% 100%;
	--popover-foreground: 240 10% 3.9%;
	--primary: 240 5.9% 10%;
	--primary-foreground: 0 0% 98%;
	--secondary: 240 4.8% 95.9%;
	--secondary-foreground: 240 5.9% 10%;
	--muted: 240 4.8% 95.9%;
	--muted-foreground: 240 3.8% 46.1%;
	--accent: 240 4.8% 95.9%;
	--accent-foreground: 240 5.9% 10%;
	--destructive: 0 84.2% 60.2%;
	--destructive-foreground: 0 0% 98%;
	--border: 240 5.9% 90%;
	--input: 240 5.9% 90%;
	--ring: 240 5.9% 10%;
	--radius: 0.5rem;
}

.dark {
	--main-background: #464553;
	--background: 240 10% 3.9%;
	--foreground: 0 0% 98%;
	--card: 240 10% 3.9%;
	--card-foreground: 0 0% 98%;
	--popover: 240 10% 3.9%;
	--popover-foreground: 0 0% 98%;
	--primary: 0 0% 98%;
	--primary-foreground: 240 5.9% 10%;
	--secondary: 240 3.7% 15.9%;
	--secondary-foreground: 0 0% 98%;
	--muted: 240 3.7% 15.9%;
	--muted-foreground: 240 5% 64.9%;
	--accent: 240 3.7% 15.9%;
	--accent-foreground: 0 0% 98%;
	--destructive: 0 62.8% 30.6%;
	--destructive-foreground: 0 0% 98%;
	--border: 240 3.7% 15.9%;
	--input: 240 3.7% 15.9%;
	--ring: 240 4.9% 83.9%;
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
		font-feature-settings: "rlig" 1, "calt" 1;
	}
}

@media (prefers-color-scheme: light) {
	:root {
		@apply text-foreground bg-background;
	}
	a:hover {
		color: #747bff;
	}
	button {
		@apply bg-primary;
	}
}
