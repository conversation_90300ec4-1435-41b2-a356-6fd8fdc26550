import * as React from "react";
import {
	getBezierPath,
	Position,
	type ConnectionLineComponent,
} from "@xyflow/react";

/**
 * ConnectionLine is a custom component for rendering the line during connection creation
 */
const ConnectionLine: ConnectionLineComponent = ({
	fromX,
	fromY,
	toX,
	toY,
	connectionStatus,
}) => {
	// Calculate the path for the connection line
	const edgePath = React.useMemo(() => {
		const [path] = getBezierPath({
			sourceX: fromX,
			sourceY: fromY,
			targetX: toX,
			targetY: toY,
			sourcePosition: Position.Right,
			targetPosition: Position.Left,
			curvature: 0.2,
		});

		return path;
	}, [fromX, fromY, toX, toY]);

	// Determine the color based on connection status
	const color =
		connectionStatus === "valid"
			? "var(--color-blue-500, #3b82f6)"
			: "var(--color-red-500, #ef4444)";

	return (
		<g>
			<path
				d={edgePath}
				fill="none"
				stroke={color}
				strokeWidth={2}
				strokeDasharray="5,5"
				className="animated"
				style={{
					animation: "flowAnimation 10s infinite linear",
				}}
			/>

			{/* Arrow at the end of the connection line */}
			<circle
				cx={toX}
				cy={toY}
				r={4}
				fill={color}
				stroke="white"
				strokeWidth={1}
			/>
		</g>
	);
};

export default ConnectionLine;
