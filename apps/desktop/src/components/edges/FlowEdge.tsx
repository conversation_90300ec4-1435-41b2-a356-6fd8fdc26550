import * as React from "react";
import { BaseEdge, EdgeLabelRenderer } from "@xyflow/react";
import type { Edge, EdgeProps } from "@xyflow/react";
import type { EdgeData } from "../../types/pipeline.types";
import { EdgeMarker } from "./EdgeMarker";
import { getEdgeRenderData } from "../../utils/edge.utils";
import { EdgeToolbar } from "./EdgeToolbar";
import { usePipelineStore } from "@stores/pipeline.store";

type FlowEdgeProps = EdgeProps<Edge<EdgeData>>;

/**
 * FlowEdge is a custom edge component that shows different styles based on node statuses
 * and provides interactive features
 */
export const FlowEdge: React.FC<FlowEdgeProps> = (props) => {
	const { id } = props;
	const { setEdges, edges } = usePipelineStore();
	const [isHovered, setIsHovered] = React.useState(false);

	const { segments, labelPosition } = getEdgeRenderData(props);

	const status = props.data?.status;
	const isRunning =
		props.data?.isRunning ||
		props.data?.sourceIsProcessing ||
		props.data?.targetIsProcessing;

	// Determine edge color based on status
	const edgeColor = React.useMemo(() => {
		// Check source node status first
		if (props.data?.sourceStatus === "success" || status === "success") {
			return "var(--color-emerald-600, #059669)"; // success green
		} else if (props.data?.sourceStatus === "error" || status === "error") {
			return "var(--color-red-500, #ef4444)"; // error red
		} else if (props.data?.sourceStatus === "loading" || isRunning) {
			return "var(--color-blue-500, #3b82f6)"; // running blue
		} else if (status === "pinned") {
			return "var(--color-orange-500, #f97316)"; // warning orange
		} else if (props.selected || isHovered) {
			return "var(--color-blue-500, #3b82f6)"; // selection blue
		} else {
			return "var(--color-gray-400, #9ca3af)"; // default gray
		}
	}, [
		status,
		props.data?.sourceStatus,
		props.data?.targetStatus,
		isRunning,
		props.selected,
		isHovered,
	]);

	// Edge style with animation for running state
	const edgeStyle = React.useMemo(
		() => ({
			...props.style,
			strokeWidth: 2,
			stroke: edgeColor,
			strokeDasharray: isRunning ? "5,5" : undefined,
			animation: isRunning ? "flowAnimation 10s infinite linear" : undefined,
		}),
		[props.style, edgeColor, isRunning],
	);

	// Add CSS animation for the flowing effect
	React.useEffect(() => {
		// Add the animation style if it doesn't exist
		if (!document.getElementById("edge-animation-style")) {
			const styleElement = document.createElement("style");
			styleElement.id = "edge-animation-style";
			styleElement.textContent = `
        @keyframes flowAnimation {
          0% {
            stroke-dashoffset: 50;
          }
          100% {
            stroke-dashoffset: 0;
          }
        }
      `;
			document.head.appendChild(styleElement);
		}
	}, []);

	// Handle edge deletion
	const handleDeleteEdge = React.useCallback(() => {
		console.log({ edges, id });
		setEdges(edges.filter((e) => e.id !== id));
	}, [id, edges]);

	return (
		<>
			<EdgeMarker
				id={props.id}
				fill={edgeColor}
				stroke={edgeColor}
				className="edge-marker"
			/>
			<g
				data-testid="flow-edge"
				data-source-node={props.source}
				data-target-node={props.target}
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
			>
				{segments.map((segment, idx) => (
					<BaseEdge
						id={`${id}-${idx}`}
						key={`${id}-${idx}-${segments.length}`}
						path={segment[0]}
						style={edgeStyle}
						className="group edge"
						markerEnd={
							idx === segments.length - 1 ? `url("#${props.id}")` : undefined
						}
						interactionWidth={40}
					/>
				))}
			</g>

			<EdgeLabelRenderer>
				<div
					style={{
						transform: `translate(-50%, -50%) translate(${labelPosition[0]}px, ${labelPosition[1]}px)`,
						pointerEvents: "all",
						position: "absolute",
						display: "flex",
						flexDirection: "column",
						alignItems: "center",
						zIndex: 1002,
					}}
					className="edge-toolbar"
					onMouseEnter={() => setIsHovered(true)}
					onMouseLeave={() => setIsHovered(false)}
				>
					{isHovered ? (
						<EdgeToolbar onDelete={handleDeleteEdge} />
					) : (
						props.data?.label && (
							<div className="edge-label px-2 py-1 bg-[#d3d2e5] rounded text-xs">
								{props.data?.label || "Connection"}
							</div>
						)
					)}
				</div>
			</EdgeLabelRenderer>
		</>
	);
};

export default FlowEdge;
