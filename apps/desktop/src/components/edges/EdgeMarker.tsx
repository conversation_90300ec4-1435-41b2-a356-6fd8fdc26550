import type * as React from "react";

interface EdgeMarkerProps extends React.SVGProps<SVGSVGElement> {
	id: string;
	fill?: string;
	stroke?: string;
}

/**
 * EdgeMarker creates an SVG marker for edge endpoints
 */
export const EdgeMarker: React.FC<EdgeMarkerProps> = ({
	id,
	fill = "currentColor",
	stroke = "currentColor",
	...rest
}) => (
	<svg fill={fill} stroke={stroke} {...rest}>
		<title>Arrow Head</title>
		<defs>
			<marker
				id={id}
				viewBox="-10 -10 20 20"
				refX="0"
				refY="0"
				markerWidth="8"
				markerHeight="8"
				markerUnits="strokeWidth"
				orient="auto-start-reverse"
			>
				<polyline
					strokeLinecap="round"
					strokeLinejoin="round"
					points="-7,-6 0,0 -7,6 -7,-6"
					strokeWidth="3"
					stroke="context-stroke"
					fill="context-stroke"
				/>
			</marker>
		</defs>
	</svg>
);
