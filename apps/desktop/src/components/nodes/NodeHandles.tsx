import type * as React from "react";
import { Position, type HandleProps } from "@xyflow/react";
import { BaseHandle } from "@components/ui/BaseHandle";
import { Button } from "@components/ui/basic_elements/button";
import { Plus } from "lucide-react";

interface NodeHandlesProps {
	inputPosition?: Position;
	outputPosition?: Position;
	showInputHandle?: boolean;
	showOutputHandle?: boolean;
}

/**
 * NodeHandles provides standardized input and output handles for nodes
 */
export const NodeHandles: React.FC<NodeHandlesProps> = ({
	inputPosition = Position.Left,
	outputPosition = Position.Right,
	showInputHandle = true,
	showOutputHandle = true,
	...props
}) => {
	return (
		<>
			{showInputHandle && (
				<BaseHandle
					type="target"
					position={inputPosition}
					className="w-2 h-4 rounded-sm !bg-stone-500"
					isConnectable={true}
					{...props}
				/>
			)}

			{showOutputHandle && (
				<BaseHandle
					type="source"
					position={outputPosition}
					className="w-2 h-2 rounded-full !bg-stone-500"
					isConnectable={true}
					{...props}
				/>
			)}
		</>
	);
};

/**
 * InputHandle provides just an input handle for nodes
 */
export const InputHandle: React.FC<Pick<NodeHandlesProps, "inputPosition">> = ({
	inputPosition = Position.Left,
}) => {
	return (
		<BaseHandle
			type="target"
			position={inputPosition}
			className="w-2 h-4 rounded-sm !bg-stone-500"
			isConnectable={true}
		/>
	);
};

/**
 * OutputHandle provides just an output handle for nodes
 */
export const OutputHandle: React.FC<
	Pick<NodeHandlesProps, "outputPosition">
> = ({ outputPosition = Position.Right }) => {
	return (
		<BaseHandle
			type="source"
			position={outputPosition}
			className="w-2 h-2 rounded-full !bg-stone-500"
			isConnectable={true}
		/>
	);
};

const wrapperClassNames: Record<Position, string> = {
	[Position.Top]:
		"flex-col-reverse left-1/2 -translate-y-full -translate-x-1/2",
	[Position.Bottom]: "flex-col left-1/2 translate-y-[10px] -translate-x-1/2",
	[Position.Left]:
		"flex-row-reverse top-1/2 -translate-x-full -translate-y-1/2",
	[Position.Right]: "top-1/2 -translate-y-1/2 translate-x-[10px]",
};

export const ButtonHandle = ({
	onClick,
	showButton = true,
	position = Position.Bottom,
	children,
	...props
}: HandleProps & { showButton?: boolean; onClick?: () => void }) => {
	const wrapperClassName = wrapperClassNames[position || Position.Bottom];
	const vertical = position === Position.Top || position === Position.Bottom;

	return (
		<BaseHandle position={position} id={props.id} {...props}>
			{showButton && (
				<div
					className={`absolute flex items-center ${wrapperClassName} pointer-events-none`}
				>
					<div
						className={`bg-gray-400 ${vertical ? "h-10 w-[2px]" : "h-[2px] w-10"}`}
					/>
					<div className="nodrag nopan pointer-events-auto">
						<Button
							onClick={onClick}
							size="sm"
							variant="secondary"
							className="!rounded-full !w-8 !h-8 !p-0"
						>
							<Plus size={10} />
						</Button>
					</div>
				</div>
			)}
		</BaseHandle>
	);
};
