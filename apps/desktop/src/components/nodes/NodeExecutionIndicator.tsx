import type * as React from "react";
import { useNodeExecutionState } from "../../stores/execution.store";
import { Clock, CheckCircle, XCircle, AlertTriangle } from "lucide-react";

interface NodeExecutionIndicatorProps {
	nodeId: string;
}

/**
 * NodeExecutionIndicator displays the execution state of a node
 */
export const NodeExecutionIndicator: React.FC<NodeExecutionIndicatorProps> = ({
	nodeId,
}) => {
	const executionState = useNodeExecutionState(nodeId);

	if (!executionState) {
		return null;
	}

	const { status, duration, isWaitingForInputs } = executionState;

	// Format duration in seconds with 1 decimal place
	const formattedDuration = duration
		? `${(duration / 1000).toFixed(1)}s`
		: null;

	return (
		<div className="flex items-center gap-1 text-xs">
			{status === "loading" && (
				<div className="flex items-center text-blue-500">
					<Clock size={14} className="animate-pulse mr-1" />
					<span>Processing...</span>
				</div>
			)}

			{status === "success" && (
				<div className="flex items-center text-emerald-500">
					<CheckCircle size={14} className="mr-1" />
					<span>Success {formattedDuration && `(${formattedDuration})`}</span>
				</div>
			)}

			{status === "error" && (
				<div className="flex items-center text-red-500">
					<XCircle size={14} className="mr-1" />
					<span>Failed {formattedDuration && `(${formattedDuration})`}</span>
				</div>
			)}

			{isWaitingForInputs && status !== "loading" && (
				<div className="flex items-center text-amber-500">
					<AlertTriangle size={14} className="mr-1" />
					<span>Waiting for inputs</span>
				</div>
			)}
		</div>
	);
};

export default NodeExecutionIndicator;
