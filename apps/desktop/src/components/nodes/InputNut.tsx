// import * as React from "react";
import { Position, useConnection, useNodeConnections } from "@xyflow/react";

import type { ConnectionState, Node, NodeProps } from "@xyflow/react";
import { ButtonHandle, NodeHandles } from "./NodeHandles";
import NutIndicatorWrapper from "@components/ui/NutIndicatorWrapper";
import type { Nuts } from "../../types/nut.types";

type CustomNodeType = Node<Nuts, "input">;

// Selector for ButtonHandle
// const selector = (connection: ConnectionState) => {
// 	return connection.inProgress;
// };

export const InputNut = (props: NodeProps<CustomNodeType>) => {
	// Condition for show button in handler
	// const connectionInProgress = useConnection(selector);
	// const connections = useNodeConnections({ handleType: "source" });

	return (
		<NutIndicatorWrapper {...props}>
			<NodeHandles
				showInputHandle={false}
				// showButton={!connectionInProgress && connections.length === 0}
				// type="source"
				// position={Position.Right}
				outputPosition={Position.Right}
			/>
		</NutIndicatorWrapper>
	);
};
