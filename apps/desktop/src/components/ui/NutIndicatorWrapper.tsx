import * as React from "react";
import type { Node as ReactFlowNode, NodeProps } from "@xyflow/react";
import { NodeStatusIndicator } from "@components/ui/StatusIndicator";
import { useNodeActions, useNodeStatus } from "@stores/node.store";
import { usePipelineStore } from "@stores/pipeline.store";
import { NodeToolbar as CustomNodeToolbar } from "@components/ui/NodeToolbar";
import { useContextMenu } from "@stores/ui.store";
import NodeExecutionIndicator from "@components/nodes/NodeExecutionIndicator";
import { useExecutionStore } from "@stores/execution.store";

// Helper function to get status color
const getStatusColor = (status: string) => {
	switch (status) {
		case "loading":
			return "bg-blue-500 animate-pulse";
		case "success":
			return "bg-emerald-500";
		case "error":
			return "bg-red-500";
		case "initial":
		default:
			return "bg-gray-300";
	}
};

type CommonNodeProps = {
	label?: string;
	icon?: string;
};

const NutIndicatorWrapper = (
	props: NodeProps<ReactFlowNode<CommonNodeProps, "common">> &
		React.PropsWithChildren,
) => {
	const { edges, setEdges } = usePipelineStore();

	const status = useNodeStatus(props.id);
	const isProcessing = React.useMemo(() => status === "loading", [status]);
	const { resetStatus: resetStatusFn } = useNodeActions();

	const { executeNode, updateNodeExecutionState } = useExecutionStore();

	const removeNode = usePipelineStore((state) => state.removeNode);

	// Update connected edges when status changes
	React.useEffect(() => {
		const outgoingEdges = edges.filter((edge) => edge.source === props.id);

		if (outgoingEdges.length > 0) {
			setEdges((edges) =>
				edges.map((edge) => {
					if (edge.source === props.id) {
						return {
							...edge,
							data: {
								...edge.data,
								status: status === "loading" ? undefined : status,
								isRunning: status === "loading",
								label: status === "error" ? "Error" : undefined,
							},
						};
					}
					return edge;
				}),
			);

			// if (status === "success") {
			// 	outgoingEdges.forEach((edge) => simulateProcessing(edge.target));
			// }
		}
	}, [status, props.id]);

	// Simulate a processing action
	const simulateProcessing = (id: string) => {
		executeNode(id);
	};

	// Reset status to initial
	const resetStatus = () => {
		resetStatusFn(props.id);
		updateNodeExecutionState(props.id, { status: "initial" });
	};

	const [showToolbar, setShowToolbar] = React.useState(false);
	const toolbarRef = React.useRef<HTMLDivElement>(null);
	const nodeRef = React.useRef<HTMLDivElement>(null);

	// Handle mouse events for toolbar visibility
	const handleMouseEnter = () => setShowToolbar(true);
	const handleMouseLeave = (e: React.MouseEvent) => {
		// Check if mouse is moving to the toolbar
		if (toolbarRef.current?.contains(e.relatedTarget as Node)) {
			return;
		}
		setShowToolbar(false);
	};

	const { isVisible, show, hide } = useContextMenu();

	const menuButtonRef = React.useRef<HTMLButtonElement>(null);

	return (
		<>
			{showToolbar && (
				<div
					ref={toolbarRef}
					className="absolute -top-8 left-1/2 transform -translate-x-1/2 z-10 bg-transparent"
					onMouseEnter={handleMouseEnter}
					onMouseLeave={handleMouseLeave}
				>
					<CustomNodeToolbar
						menuButtonRef={menuButtonRef}
						onProcess={() => simulateProcessing(props.id)}
						onReset={resetStatus}
						onDelete={() => removeNode(props.id)}
						onExtra={(evt) =>
							isVisible
								? hide()
								: show(props.id, {
										top:
											(menuButtonRef.current?.getBoundingClientRect().bottom ??
												0) + 5,
										left:
											(menuButtonRef.current?.getBoundingClientRect().left ??
												0) - 220,
									})
						}
						isProcessing={isProcessing}
						isResetDisabled={status === "initial" || isProcessing}
					/>
				</div>
			)}
			<NodeStatusIndicator key={status} status={status}>
				<div
					ref={nodeRef}
					className="bg-white p-4 rounded-md shadow-md border-0 w-[200px] transition-all duration-200 ease-in-out hover:shadow-lg"
					onMouseEnter={handleMouseEnter}
					onMouseLeave={handleMouseLeave}
				>
					{/* Node Header */}
					<div className="flex items-center justify-between mb-3">
						<div className="font-medium text-sm text-gray-800">
							{(props.data?.label as string) || "Indicator Node"}
						</div>
						<div className="flex items-center">
							<div
								className={`w-3 h-3 rounded-full ${getStatusColor(status)}`}
								title={`Status: ${status}`}
							/>
						</div>
					</div>

					{/* Node Content */}
					<div className="bg-gray-50 p-2 rounded-lg mb-3">
						<div className="text-xs text-gray-600 flex items-center justify-between">
							<span>Status:</span>
							<span className="font-medium capitalize">{status}</span>
						</div>
						{isProcessing && (
							<div className="mt-2 w-full bg-gray-200 rounded-full h-1.5">
								<div className="bg-blue-500 h-1.5 rounded-full animate-pulse-width" />
							</div>
						)}

						{/* Node Execution Indicator */}
						<div className="mt-2">
							<NodeExecutionIndicator nodeId={props.id} />
						</div>
					</div>

					{props.children}
				</div>
			</NodeStatusIndicator>
		</>
	);
};

export default NutIndicatorWrapper;
