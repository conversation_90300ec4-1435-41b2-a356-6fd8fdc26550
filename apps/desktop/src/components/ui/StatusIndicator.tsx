import React, { type ReactNode } from "react";

export type NodeStatusIndicatorProps = {
	status?: "loading" | "success" | "error" | "initial";
	children: ReactNode;
};

export const LoadingIndicator = ({ children }: { children: ReactNode }) => {
	return (
		<>
			<div className="absolute -left-[1px] -top-[1px] h-[calc(100%+2px)] w-[calc(100%+2px)]">
				<style>
					{`
        @keyframes spin {
          from { transform: translate(-50%, -50%) rotate(0deg); }
          to { transform: translate(-50%, -50%) rotate(360deg); }
        }
        .spinner {
          animation: spin 2s linear infinite;
          position: absolute;
          left: 50%;
          top: 50%;
          width: 140%;
          aspect-ratio: 1;
          transform-origin: center;
        }
         @keyframes pulseWidth {
            0% { width: 0; }
            100% { width: 100%; }
         }
         .animate-pulse-width {
            animation: pulseWidth 1.5s ease-in-out infinite;
         }
      `}
				</style>
				<div className="absolute inset-0 overflow-hidden rounded-md -z-10">
					<div className="spinner rounded-full bg-[conic-gradient(from_0deg_at_50%_50%,_rgb(42,67,233)_0deg,_rgba(42,138,246,0)_360deg)]" />
				</div>
			</div>
			{children}
		</>
	);
};

const StatusBorder = ({
	children,
	className,
}: {
	children: ReactNode;
	className?: string;
}) => {
	return (
		<>
			<div
				className={`absolute -left-[1px] -top-[1px] h-[calc(100%+2px)] w-[calc(100%+2px)] rounded-md border-2 -z-10 ${className}`}
			/>
			{children}
		</>
	);
};

export const NodeStatusIndicator = ({
	status,
	children,
}: NodeStatusIndicatorProps) => {
	switch (status) {
		case "loading":
			return <LoadingIndicator>{children}</LoadingIndicator>;
		case "success":
			return (
				<StatusBorder className="border-emerald-600 bg-emerald-50/30">
					{children}
				</StatusBorder>
			);
		case "error":
			return (
				<StatusBorder className="border-red-500 bg-red-50/30">
					{children}
				</StatusBorder>
			);
		case "initial":
			return (
				<StatusBorder className="border-gray-300">{children}</StatusBorder>
			);
		default:
			return <>{children}</>;
	}
};
