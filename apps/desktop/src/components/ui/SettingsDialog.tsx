import * as React from "react";
import {
	useEditDialog,
	useUIStore,
	useViewSettings,
} from "../../stores/ui.store";
import { usePipelineStore } from "../../stores/pipeline.store";
import { Monitor, Palette, X } from "lucide-react";
import { But<PERSON> } from "./basic_elements/button";
import {
	<PERSON><PERSON>,
	<PERSON>alogContent,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "./basic_elements/dialog";

import { LayoutDirection } from "../../types/pipeline.types";
import { Badge } from "./basic_elements/badge";

interface NodeEditDialogProps {
	className?: string;
}

const themes = [
	{
		id: "light" as const,
		name: "Light",
		description: "Clean and bright interface",
		preview: "bg-white border-2 border-gray-200",
		dot: "bg-gray-100",
	},
	{
		id: "dark" as const,
		name: "Dark",
		description: "Easy on the eyes",
		preview: "bg-gray-900 border-2 border-gray-700",
		dot: "bg-gray-600",
	},
];
/**
 * NodeEditDialog provides a dialog for editing node properties
 */
export const SettingsDialog: React.FC<NodeEditDialogProps> = () => {
	const { close } = useEditDialog();
	const isSettingsDialogOpen = useUIStore(
		(state) => state.isSettingsDialogOpen,
	);
	const { isDarkMode, toggleDarkMode } = useViewSettings();

	const { applyLayout, setLayoutDirection, fitView } = usePipelineStore();

	// Handle changing layout direction
	const handleChangeLayout = React.useCallback(
		(direction: LayoutDirection) => {
			setLayoutDirection(direction);
			applyLayout();
		},
		[setLayoutDirection, applyLayout],
	);

	// If dialog is not open or no node is selected, don't render
	if (!isSettingsDialogOpen) {
		return null;
	}

	return (
		<Dialog
			open={isSettingsDialogOpen}
			onOpenChange={(open) => !open && close()}
		>
			<DialogContent className="!p-0">
				<DialogHeader>
					<div className="flex items-center justify-between p-6 border-b border-gray-200">
						<div className="flex items-center gap-3">
							<div className="p-2 bg-blue-100 rounded-lg">
								<Monitor className="w-5 h-5 text-blue-600" />
							</div>
							<DialogTitle className="flex flex-col items-start justify-between">
								<p className="text-xl font-semibold text-gray-900">Settings</p>
								<p className="text-sm text-gray-500">
									Customize your workspace
								</p>
							</DialogTitle>
						</div>
						<Button
							variant="ghost"
							size="icon"
							onClick={close}
							className="!rounded-full"
						>
							<X className="w-4 h-4" />
						</Button>
					</div>
				</DialogHeader>

				<div className="p-6 overflow-y-auto">
					<div className="space-y-6">
						<div className="flex items-center gap-2 mb-4">
							<Palette className="w-5 h-5 text-gray-600" />
							<h3 className="text-lg font-medium text-gray-900">Board Theme</h3>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{themes.map((themeOption) => (
								<button
									type="button"
									key={themeOption.id}
									onClick={() => toggleDarkMode()}
									className={`relative p-4 rounded-lg border-2 transition-all hover:shadow-md ${
										(isDarkMode && themeOption.id === "dark") ||
										(!isDarkMode && themeOption.id === "light")
											? "border-blue-500 bg-blue-50"
											: "border-gray-200 hover:border-gray-300"
									}`}
								>
									<div className="flex items-start gap-3">
										{/* Theme Preview */}
										<div
											className={`w-12 h-12 rounded-lg ${themeOption.preview} flex items-center justify-center`}
										>
											<div
												className={`w-3 h-3 rounded-full ${themeOption.dot}`}
											/>
										</div>

										{/* Theme Info */}
										<div className="flex-1 text-left">
											<div className="flex items-center gap-2 mb-1">
												<span className="font-medium text-gray-900">
													{themeOption.name}
												</span>
												{((isDarkMode && themeOption.id === "dark") ||
													(!isDarkMode && themeOption.id === "light")) && (
													<Badge variant="secondary" className="text-xs">
														Active
													</Badge>
												)}
											</div>
											<p className="text-sm text-gray-500">
												{themeOption.description}
											</p>
										</div>
									</div>
								</button>
							))}
						</div>
						<Button
							variant="secondary"
							size="lg"
							type="button"
							onClick={() => {
								handleChangeLayout(LayoutDirection.VERTICAL);
								fitView({ padding: "50px" });
							}}
						>
							vertical
						</Button>
						<Button
							variant="secondary"
							size="lg"
							type="button"
							onClick={() => {
								handleChangeLayout(LayoutDirection.HORIZONTAL);
								fitView({ padding: "50px" });
							}}
						>
							horizontal
						</Button>
						{/* Additional Settings */}
						<div className="pt-4 border-t border-gray-200">
							<h3 className="text-lg font-medium text-gray-900 mb-4">
								Canvas Settings
							</h3>
							<div className="flex flex-row items-center justify-center gap-3">
								<div className="flex flex-1/2 items-center justify-between p-3 bg-gray-50 rounded-lg">
									<div>
										<span className="font-medium text-gray-900">Grid</span>
										<p className="text-sm text-gray-500">
											Show background grid
										</p>
									</div>
									<div className="w-4 h-4 bg-green-500 rounded-full"></div>
								</div>

								<div className="flex flex-1/2 items-center justify-between p-3 bg-gray-50 rounded-lg">
									<div>
										<span className="font-medium text-gray-900">
											Snap to Grid
										</span>
										<p className="text-sm text-gray-500">Align nodes to grid</p>
									</div>
									<div className="w-4 h-4 bg-gray-300 rounded-full"></div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<DialogFooter className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
					<Button variant="outline" onClick={close}>
						Cancel
					</Button>
					<Button onClick={close} className="bg-blue-600 hover:bg-blue-700">
						Save Changes
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};
