import React from "react";
import { Button } from "@components/ui/basic_elements/button";
import {
	info,
	warn,
	error,
	debug,
	success,
	logPipelineStart,
	logPipelineEnd,
	logNodeStart,
	logNodeSuccess,
	logNodeError,
	logPerformance,
} from "@stores/log.store";

export function LoggerTestPanel() {
	const testBasicLogs = () => {
		info("This is an info message", { testData: "info test" }, "TestPanel");
		warn(
			"This is a warning message",
			{ testData: "warning test" },
			"TestPanel",
		);
		error("This is an error message", { testData: "error test" }, "TestPanel");
		debug("This is a debug message", { testData: "debug test" }, "TestPanel");
		success(
			"This is a success message",
			{ testData: "success test" },
			"TestPanel",
		);
	};

	const testPipelineLogs = () => {
		const executionId = `test-exec-${Date.now()}`;

		// Start pipeline
		logPipelineStart(executionId, 3);

		// Simulate node executions
		setTimeout(() => {
			logNodeStart("node-1", executionId, "InputNode");
			setTimeout(() => {
				logNodeSuccess("node-1", executionId, 1200, {
					outputData: "processed input",
				});

				logNodeStart("node-2", executionId, "ProcessNode");
				setTimeout(() => {
					logNodeSuccess("node-2", executionId, 800, {
						outputData: "processed data",
					});

					logNodeStart("node-3", executionId, "OutputNode");
					setTimeout(() => {
						logNodeSuccess("node-3", executionId, 600, {
							outputData: "final result",
						});

						// End pipeline
						logPipelineEnd(executionId, 2600, 3, 0);
					}, 600);
				}, 800);
			}, 1200);
		}, 100);
	};

	const testErrorScenario = () => {
		const executionId = `error-exec-${Date.now()}`;

		logPipelineStart(executionId, 2);

		setTimeout(() => {
			logNodeStart("node-1", executionId, "InputNode");
			setTimeout(() => {
				logNodeSuccess("node-1", executionId, 1000, {
					outputData: "input processed",
				});

				logNodeStart("node-2", executionId, "ProcessNode");
				setTimeout(() => {
					logNodeError(
						"node-2",
						executionId,
						500,
						"Processing failed: Invalid input format",
						{
							errorCode: "INVALID_FORMAT",
							inputData: { type: "unknown" },
						},
					);

					logPipelineEnd(executionId, 1500, 1, 1);
				}, 500);
			}, 1000);
		}, 100);
	};

	const testPerformanceLogs = () => {
		logPerformance("Data Processing", 2500, {
			recordsProcessed: 10000,
			memoryUsage: 256,
			cpuUsage: 75,
		});

		logPerformance("File Upload", 1200, {
			fileSize: "5MB",
			uploadSpeed: "4.2MB/s",
		});
	};

	return (
		<div className="p-4 bg-gray-800 border border-gray-600 rounded-lg">
			<h3 className="text-lg font-semibold text-white mb-4">
				Logger Test Panel
			</h3>
			<div className="grid grid-cols-2 gap-3">
				<Button onClick={testBasicLogs} variant="outline" className="text-sm">
					Test Basic Logs
				</Button>

				<Button
					onClick={testPipelineLogs}
					variant="outline"
					className="text-sm"
				>
					Test Pipeline Success
				</Button>

				<Button
					onClick={testErrorScenario}
					variant="outline"
					className="text-sm"
				>
					Test Pipeline Error
				</Button>

				<Button
					onClick={testPerformanceLogs}
					variant="outline"
					className="text-sm"
				>
					Test Performance Logs
				</Button>
			</div>
		</div>
	);
}
