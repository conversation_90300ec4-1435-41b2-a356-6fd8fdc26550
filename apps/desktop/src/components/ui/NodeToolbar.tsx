import type * as React from "react";
import { Play, StopCircle, MoreHorizontal, Trash2 } from "lucide-react";
import { Button } from "./basic_elements/button";

interface NodeToolbarProps {
	menuButtonRef?: React.RefObject<HTMLButtonElement | null>;
	onProcess: () => void;
	onReset: () => void;
	onDelete?: () => void;
	onExtra?: (evt: React.MouseEvent) => void;
	isProcessing: boolean;
	isResetDisabled: boolean;
}

export const NodeToolbar: React.FC<NodeToolbarProps> = ({
	menuButtonRef,
	onProcess,
	onReset,
	onExtra,
	onDelete,
	isProcessing,
	isResetDisabled,
}) => {
	return (
		<div className="flex gap-1 p-1 bg-white/0 backdrop-blur-sm rounded-md shadow-sm border">
			<Button
				variant="ghost"
				size="sm"
				type="button"
				onClick={onProcess}
				disabled={isProcessing}
				className="!h-7 !w-7 px-2 hover:bg-blue-50 !grid !place-content-center"
				title="Process node"
			>
				<Play size={12} className="text-blue-600" />
			</Button>

			{onDelete && (
				<Button
					variant="ghost"
					size="sm"
					type="button"
					onClick={onDelete}
					className="!h-7 !w-7 px-2 hover:bg-gray-400/20 !grid !place-content-center"
					title="Delete node"
				>
					<Trash2 size={12} className="stroke-red-400" />
				</Button>
			)}

			<Button
				variant="ghost"
				size="sm"
				type="button"
				onClick={onReset}
				disabled={isResetDisabled}
				className="!h-7 !w-7 px-2 hover:bg-gray-400/20 !grid !place-content-center"
				title="Reset status"
			>
				<StopCircle size={12} className="text-gray-600" />
			</Button>

			{onExtra && (
				<Button
					ref={menuButtonRef}
					variant="ghost"
					size="sm"
					type="button"
					onClick={(evt) => onExtra(evt)}
					className="!h-7 !w-7 px-2 hover:bg-gray-400/20 !grid !place-content-center"
					title="More options"
				>
					<MoreHorizontal size={12} className="text-gray-600" />
				</Button>
			)}
		</div>
	);
};
