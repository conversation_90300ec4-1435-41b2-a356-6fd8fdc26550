import * as React from "react";
import { useEditDialog } from "../../stores/ui.store";
import { usePipelineStore } from "../../stores/pipeline.store";
import { X } from "lucide-react";
import { Button } from "./basic_elements/button";
import { Input } from "./basic_elements/input";
import { Label } from "./basic_elements/label";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "./basic_elements/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "./basic_elements/select";
import { NutTypes } from "../../types/pipeline.types";

interface NodeEditDialogProps {
	className?: string;
}

/**
 * NodeEditDialog provides a dialog for editing node properties
 */
export const NodeEditDialog: React.FC<NodeEditDialogProps> = ({
	className = "",
}) => {
	const { node, isOpen, close } = useEditDialog();
	const { updateNode } = usePipelineStore();

	const [formData, setFormData] = React.useState({
		label: "",
		type: "",
		description: "",
	});

	// Initialize form when node changes
	React.useEffect(() => {
		if (node) {
			setFormData({
				label: (node.data?.label as string) || "",
				description: (node.data?.description as string) || "",
				type: node.type ?? "default",
			});
		}
	}, [node]);

	// Handle form submission
	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();

		if (node) {
			updateNode(
				node.id,
				{ type: formData.type },
				{
					label: formData.label,
					description: formData.description,
				},
			);
			close();
		}
	};

	// If dialog is not open or no node is selected, don't render
	if (!isOpen || !node) {
		return null;
	}

	return (
		<Dialog open={isOpen} onOpenChange={(open) => !open && close()}>
			<DialogContent className="sm:max-w-[425px]">	
				<DialogHeader>
					<DialogTitle className="flex items-center justify-between">
						Edit Node
						<Button
							variant="ghost"
							size="icon"
							className="!grid"
							onClick={close}
						>
							<X className="h-4 w-4" size={14} />
						</Button>
					</DialogTitle>
					<DialogDescription>
						Modify the properties of this node. Click save when you're done.
					</DialogDescription>
				</DialogHeader>

				<form onSubmit={handleSubmit} className="space-y-4 py-4">
					<div className="space-y-2">
						<Label htmlFor="label">Label</Label>
						<Input
							id="label"
							value={formData.label}
							onChange={(e) =>
								setFormData({ ...formData, label: e.target.value })
							}
							placeholder="Node Label"
						/>
					</div>

					<div className="space-y-2">
						<Label htmlFor="type">Type</Label>
						<Select
							value={formData.type}
							onValueChange={(value) =>
								setFormData({ ...formData, type: value })
							}
						>
							<SelectTrigger id="type">
								<SelectValue placeholder="Select node type" />
							</SelectTrigger>
							<SelectContent>
								{NutTypes.map((nut) => (
									<SelectItem key={nut} value={nut}>
										{nut}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div className="space-y-2">
						<Label htmlFor="description">Description (Optional)</Label>
						<Input
							id="description"
							value={formData.description}
							onChange={(e) =>
								setFormData({ ...formData, description: e.target.value })
							}
							placeholder="Node description"
						/>
					</div>

					<DialogFooter>
						<Button type="button" variant="outline" onClick={close}>
							Cancel
						</Button>
						<Button type="submit">Save Changes</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
};
