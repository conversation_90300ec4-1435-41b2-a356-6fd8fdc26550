import * as React from "react";
import { invoke } from "@tauri-apps/api/core";
import { open, save } from "@tauri-apps/plugin-dialog";
import { readTextFile, writeTextFile } from "@tauri-apps/plugin-fs";
import { usePipelineStore } from "@stores/pipeline.store";
import { useNodeStore } from "@stores/node.store";
import { LayoutDirection, NodeType } from "../../types/pipeline.types";
import { Button } from "@components/ui/basic_elements/button";
import { Download, Upload } from "lucide-react";

interface ValidationResult {
	is_valid: boolean;
	errors: string[];
	pipeline?: any;
}

interface PipelineStructure {
	nodes: Array<{
		id: string;
		nut_type: NodeType;
		description?: string;
		input_types: string[];
		output_types: string[];
		data: Record<string, unknown>;
	}>;
	edges: Array<{
		source: string;
		target: string;
	}>;
}

interface PipelineUploaderProps {
	showTitle?: boolean;
	title?: string;
	showStructure?: boolean;
}

export default function PipelineUploader(props: PipelineUploaderProps) {
	const fileName = usePipelineStore((state) => state.fileName);
	const setFileName = usePipelineStore((state) => state.setPipelineFile);
	const { nodes, edges } = usePipelineStore();

	// usePipelineStore.subscribe(
	// 	(state) => state.fileName,
	// 	(fileName, _) => {
	// 		console.log("subscriber to file name", fileName);
	// 		readTextFile(fileName).then((content) => {
	// 			setFile(content);

	// 			// Reset states
	// 			setValidationResult(null);
	// 			setPipelineStructure(null);
	// 			setError(null);
	// 		});
	// 	},
	// );

	const [file, setFile] = React.useState<string | null>(null);
	const [validationResult, setValidationResult] =
		React.useState<ValidationResult | null>(null);
	const [pipelineStructure, setPipelineStructure] =
		React.useState<PipelineStructure | null>(null);
	const [loading, setLoading] = React.useState<boolean>(false);
	const [error, setError] = React.useState<string | null>(null);

	const { setNodes, setEdges, applyLayout, setLayoutDirection } =
		usePipelineStore();
	const { createNodeWithId } = useNodeStore();

	React.useEffect(() => {
		const nuts = pipelineStructure?.nodes.map((node) =>
			// Node type should be: node.nut_type
			createNodeWithId(
				node.id,
				node.nut_type || NodeType.INDICATOR,
				{ x: 0, y: 0 },
				{ ...node, ...node.data },
			),
		);
		const edges = pipelineStructure?.edges.map((edge) => ({
			...edge,
			type: "custom-edge",
			id: `${edge.source}--${edge.target}`,
		}));

		setNodes(nuts ?? []);
		setEdges(edges ?? []);
		setLayoutDirection(LayoutDirection.HORIZONTAL);
		applyLayout();
	}, [pipelineStructure]);

	React.useEffect(() => {
		// Load file content when fileName changes
		if (fileName) {
			readTextFile(fileName)
				.then((content) => {
					setFile(content);
					validateAndLoadPipeline(content);
				})
				.catch((err) => {
					setError(`Error reading file: ${err}`);
				});
		}
	}, [fileName]);

	const validateAndLoadPipeline = async (content: string) => {
		setLoading(true);
		setError(null);

		try {
			// Validate the pipeline JSON
			const result: ValidationResult = await invoke("validate_pipeline_json", {
				jsonStr: content,
			});
			setValidationResult(result);

			// If valid, build the pipeline structure
			if (result.is_valid) {
				const structure: PipelineStructure = await invoke(
					"build_pipeline_structure_from_json",
					{ jsonStr: content },
				);
				console.log({ structure });
				setPipelineStructure(structure);
				return structure;
			} else {
				console.log({ result });
				setError(`Error validating pipeline: ${result.errors[0]}`);
			}
		} catch (err) {
			setError(`Error validating pipeline: ${err}`);
		} finally {
			setLoading(false);
		}
	};

	const handleFileSelect = async () => {
		try {
			const selected = await open({
				multiple: false,
				filters: [{ name: "JSON", extensions: ["json"] }],
			});

			if (selected && !Array.isArray(selected)) {
				setFileName(selected);
			}
		} catch (err) {
			setError(`Error selecting file: ${err}`);
		}
	};

	const handleExport = async () => {
		try {
			if (nodes.length === 0) {
				setError("No pipeline to export");
				return;
			}

			// Convert current pipeline to JSON format
			const pipeline = {
				id: `pipeline_${Date.now()}`,
				name: "Exported Pipeline",
				init: nodes[0]?.id || "",
				nuts: Object.fromEntries(
					nodes.map((node) => [
						node.id,
						{
							type: node.data.nut_type || NodeType.INDICATOR,
							parameters: {},
							description: node.data.description || "",
							inputType: node.data.input_types || [],
							outputType: node.data.output_types || [],
							next: edges
								.filter((e) => e.source === node.id)
								.map((e) => e.target),
						},
					]),
				),
			};

			const pipelineJson = JSON.stringify(pipeline, null, 2);

			const savePath = await save({
				filters: [{ name: "JSON", extensions: ["json"] }],
			});

			if (savePath) {
				await writeTextFile(savePath, pipelineJson);
			}
		} catch (err) {
			setError(`Error exporting pipeline: ${err}`);
		}
	};

	return (
		<div className="flex flex-row gap-3">
			{props.showTitle ? <h2>{props.title ?? "Pipeline Uploader"}</h2> : null}

			<Button
				variant="outline"
				size="sm"
				onClick={handleFileSelect}
				disabled={loading}
			>
				<Upload className="w-4 h-4 mr-2" />
				Import
			</Button>
			<Button
				variant="outline"
				size="sm"
				onClick={handleExport}
				disabled={loading || nodes.length === 0}
			>
				<Download className="w-4 h-4 mr-2" />
				Export
			</Button>

			{/* <div className="upload-section">
				<Button variant="default" onClick={handleFileSelect} disabled={loading}>
					Select Pipeline JSON File
				</Button>

				{fileName && (
					<div className="flex flex-col flex-wrap gap-3 mt-3">
						<p>Selected file: {fileName}</p>
						<div className="flex flex-row gap-3 justify-center items-center">
							<Button
								variant="outline"
								onClick={validateFile}
								disabled={loading}
							>
								Validate File
							</Button>
							<Button
								variant="outline"
								onClick={validatePipeline}
								disabled={loading || !file}
							>
								Validate JSON Content
							</Button>
						</div>
					</div>
				)}

				{loading && <p>Loading...</p>}

				{error && (
					<div className="text-red-600 p-2.5 bg-red-100 rounded-sm mt-2.5">
						<p>Error: {error}</p>
					</div>
				)}
			</div>

			{validationResult && (
				<div className="validation-results">
					<h3>Validation Results</h3>
					{validationResult.is_valid ? (
						<p className="valid">Pipeline is valid! ✅</p>
					) : (
						<div className="invalid">
							<p>Pipeline is invalid! ❌</p>
							<ul className="error-list">
								{validationResult.errors.map((error, index) => (
									<li key={index}>{error}</li>
								))}
							</ul>
						</div>
					)}
				</div>
			)} */}

			{props.showStructure && pipelineStructure && (
				<div className="structure-summary">
					<h3>Pipeline Structure</h3>
					<p>Nodes: {pipelineStructure.nodes.length}</p>
					<p>Edges: {pipelineStructure.edges.length}</p>
				</div>
			)}
		</div>
	);
}
