import * as React from "react";
import { useNodeStore } from "../../stores/node.store";
import { usePipelineStore } from "../../stores/pipeline.store";
import { NodeType } from "../../types/pipeline.types";
import {
	ChevronRight,
	ChevronDown,
	Box,
	Cpu,
	Database,
	ArrowRightLeft,
	FileText,
	Settings,
} from "lucide-react";
import { useDnD } from "../../utils/dndContext";
import { Button } from "@components/ui/basic_elements/button";
import { Card } from "@components/ui/basic_elements/card";
import { useSidebar, useUIStore } from "@stores/ui.store";

// import pleros from "../../assets/pleros.svg";

// Node category interface
interface NodeCategory {
	name: string;
	icon: React.ReactNode;
	nodeTypes: {
		type: NodeType;
		name: string;
		description: string;
		icon: React.ReactNode;
	}[];
}

// Node item component
const NodeItem: React.FC<{
	type: NodeType;
	name: string;
	description: string;
	icon: React.ReactNode;
}> = ({ type, name, description, icon }) => {
	const { createNode } = useNodeStore();
	const { addNode, fitView } = usePipelineStore();

	const [_, setType] = useDnD();

	const onDragStart = (
		event: React.DragEvent<HTMLDivElement>,
		nodeType: NodeType,
	) => {
		setType(nodeType);
		event.dataTransfer.effectAllowed = "move";
	};

	const handleClick = () => {
		// Create a new node at a random position
		const newNode = createNode(
			type,
			{ x: 100 + Math.random() * 200, y: 100 + Math.random() * 100 },
			{ label: name },
		);
		addNode(newNode);
		fitView({ padding: 50 });
	};

	return (
		// <button
		// 	type="button"
		// 	tabIndex={0}
		// 	draggable
		// 	onClick={handleClick}
		// 	onDragStart={(event) => onDragStart(event, type)}
		// 	onKeyDown={(e) => {
		// 		if (e.key === "Enter" || e.key === " ") {
		// 			e.preventDefault();
		// 			handleClick();
		// 		}
		// 	}}
		// 	className="flex items-center !p-2 rounded-md cursor-pointer mb-1 hover:bg-gray-100 transition-colors text-wrap"
		// >
		// 	<div className="mr-2 text-gray-600">{icon}</div>
		// 	<div>
		// 		<div className="text-sm font-medium">{name}</div>
		// 		<div className="text-xs text-gray-500">{description}</div>
		// 	</div>
		// </button>
		<div
			key={name}
			draggable
			onDragStart={(e) => onDragStart(e, type)}
			className="text-left p-3 border-b border-gray-50 dark:border-slate-500 last:border-b-0 cursor-grab active:cursor-grabbing hover:bg-blue-50 dark:hover:bg-slate-700 transition-colors group"
		>
			<div className="font-medium text-sm text-gray-900 dark:text-gray-200 group-hover:text-blue-700 dark:group-hover:text-blue-400">
				{name}
			</div>
			<div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
				{description}
			</div>
		</div>
	);
};

// Node category component
const NodeCategorySection: React.FC<{ category: NodeCategory }> = ({
	category,
}) => {
	const [isOpen, setIsOpen] = React.useState(false);
	const contentRef = React.useRef<HTMLDivElement>(null);
	const [contentHeight, setContentHeight] = React.useState<number>(0);

	React.useEffect(() => {
		if (contentRef.current) {
			setContentHeight(contentRef.current.scrollHeight);
		}
	}, [category.nodeTypes]);

	return (
		<Card key={category.name} className="overflow-hidden">
			<button
				type="button"
				tabIndex={0}
				className={`w-full p-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-slate-600 transition-colors ${isOpen ? "bg-gray-100 dark:bg-slate-500" : ""}`}
				onClick={() => setIsOpen(!isOpen)}
				onKeyDown={(e) => {
					if (e.key === "Enter" || e.key === " ") {
						e.preventDefault();
						setIsOpen(!isOpen);
					}
				}}
			>
				<div className="flex items-center gap-3">
					{category.icon}
					<span className="font-medium text-gray-900 dark:text-gray-200">
						{category.name}
					</span>
				</div>
				{isOpen ? (
					<ChevronDown className="w-4 h-4 text-gray-500 dark:text-gray-200" />
				) : (
					<ChevronRight className="w-4 h-4 text-gray-500 dark:text-gray-200" />
				)}
			</button>

			<div
				ref={contentRef}
				style={{ maxHeight: isOpen ? `${contentHeight}px` : "0px" }}
				className="border-t border-gray-100 dark:border-slate-500"
			>
				{category.nodeTypes.map((nodeType) => (
					<NodeItem
						key={nodeType.name}
						type={nodeType.type}
						name={nodeType.name}
						description={nodeType.description}
						icon={nodeType.icon}
					/>
				))}
			</div>
		</Card>
	);
};

// Node categories data
const nodeCategories: NodeCategory[] = [
	{
		name: "Input/Output",
		icon: <Database size={18} />,
		nodeTypes: [
			{
				type: NodeType.INPUT,
				name: "Input Node",
				description: "Source of data for the pipeline",
				icon: <Database size={16} />,
			},
			{
				type: NodeType.OUTPUT,
				name: "Output Node",
				description: "Final destination for processed data",
				icon: <FileText size={16} />,
			},
		],
	},
	{
		name: "Processing",
		icon: <Cpu size={18} />,
		nodeTypes: [
			{
				type: NodeType.INDICATOR,
				name: "Processing Node",
				description: "Transforms or processes data",
				icon: <Cpu size={16} />,
			},
			{
				type: NodeType.TOOL_CUSTOM,
				name: "Filter Node",
				description: "Filters data based on conditions",
				icon: <ArrowRightLeft size={16} />,
			},
		],
	},
	{
		name: "Custom",
		icon: <Box size={18} />,
		nodeTypes: [
			{
				type: NodeType.DEFAULT,
				name: "Custom Node",
				description: "Create your own node type",
				icon: <Box size={16} />,
			},
		],
	},
];

// Main NodeSidebar component
export const NodeSidebar: React.FC = () => {
	const { isOpen } = useSidebar();
	const openSettingsDialog = useUIStore((state) => state.openSettingsDialog);

	console.log({ isOpen });
	if (!isOpen) {
		return (
			<aside className="w-0 h-full bg-white dark:bg-slate-700 transition-all duration-300" />
		);
	}

	return (
		<aside className="w-80 bg-white dark:bg-slate-800 border-r border-gray-200 dark:border-slate-600 flex flex-col transition-all duration-300">
			<div className="py-3 px-6 border-b border-gray-100 dark:border-slate-600 text-left">
				<h2 className="text-lg font-semibold text-gray-900 dark:text-gray-200 mb-2">
					Node Library
				</h2>
				<p className="text-sm text-gray-600 dark:text-gray-400">
					Drag nodes to the canvas to build your workflow
				</p>
			</div>

			<div className="flex-1 overflow-y-auto p-4 space-y-2">
				{nodeCategories.map((category) => (
					<NodeCategorySection key={category.name} category={category} />
				))}
			</div>

			<div className="py-3 px-4 border-t border-gray-100 dark:border-slate-600">
				<Button
					variant="outline"
					size="sm"
					className="w-full"
					onClick={openSettingsDialog}
				>
					<Settings className="w-4 h-4 mr-2" />
					Settings
				</Button>
			</div>
		</aside>
	);
};

export default NodeSidebar;
