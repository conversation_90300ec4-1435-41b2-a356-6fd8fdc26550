import type * as React from "react";
import { Play, Square, Pause, RotateCcw } from "lucide-react";
import { Button } from "../ui/basic_elements/button";
import {
	useExecutionControls,
	useExecutionStatus,
	useExecutionProgress,
} from "../../stores/execution.store";

/**
 * PipelineExecutionControl provides controls for executing the entire pipeline
 */
export const PipelineExecutionControl: React.FC = () => {
	const {
		startExecution,
		pauseExecution,
		resumeExecution,
		stopExecution,
		resetExecution,
	} = useExecutionControls();
	const { isRunning, isPaused, progress } = useExecutionStatus();
	const { successCount, errorCount, totalNodesCount } = useExecutionProgress();

	// Format progress as percentage
	const progressPercentage = Math.round(progress);

	return (
		<div className="flex items-center gap-2 mr-4 mt-2">
			{/* Execution controls */}
			<div className="flex items-center gap-1">
				{!isRunning ? (
					<Button
						variant="default"
						size="sm"
						type="button"
						onClick={startExecution}
						className="flex items-center gap-2 px-3 py-1 rounded-md transition-colors bg-green-600 hover:bg-green-700 text-white border-green-600"
					>
						<Play size={16} />
						<span>Run</span>
					</Button>
				) : isPaused ? (
					<Button
						variant="outline"
						size="sm"
						type="button"
						onClick={resumeExecution}
						className="flex items-center gap-2 px-3 py-1 rounded-md transition-colors bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
					>
						<Play size={16} />
						<span>Resume</span>
					</Button>
				) : (
					<Button
						variant="default"
						size="sm"
						type="button"
						onClick={pauseExecution}
						className="flex items-center gap-2 px-3 py-1 rounded-md transition-colors bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600"
					>
						<Pause size={16} />
						<span>Pause</span>
					</Button>
				)}

				{isRunning && (
					<Button
						variant="default"
						size="sm"
						type="button"
						onClick={stopExecution}
						className="flex items-center gap-2 px-3 py-1 rounded-md transition-colors bg-red-600 hover:bg-red-700 text-white border-red-600"
					>
						<Square size={16} />
						<span>Stop</span>
					</Button>
				)}

				<Button
					variant="secondary"
					size="sm"
					type="button"
					onClick={resetExecution}
					disabled={isRunning && !isPaused}
					className="flex items-center gap-2 px-3 py-1 rounded-md transition-colors bg-gray-600 hover:bg-gray-700 text-slate-700 border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					<RotateCcw size={16} />
					<span>Reset</span>
				</Button>
			</div>

			{/* Execution status */}
			<div className="flex items-center gap-2 ml-4 text-sm">
				{isRunning && (
					<div className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-md">
						<div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
							<div
								className="h-full bg-blue-500 rounded-full transition-all duration-300 ease-in-out"
								style={{ width: `${progressPercentage}%` }}
							/>
						</div>
						<span className="text-xs text-gray-700">{progressPercentage}%</span>
					</div>
				)}

				{(successCount > 0 || errorCount > 0) && (
					<div className="flex items-center gap-2 text-xs">
						<span className="text-emerald-600 font-medium">
							{successCount} success
						</span>
						{errorCount > 0 && (
							<span className="text-red-600 font-medium">
								{errorCount} failed
							</span>
						)}
						<span className="text-gray-500">of {totalNodesCount}</span>
					</div>
				)}
			</div>
		</div>
	);
};

export default PipelineExecutionControl;
