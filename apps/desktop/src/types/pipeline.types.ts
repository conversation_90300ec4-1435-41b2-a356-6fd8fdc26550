import type { Edge, Node } from "@xyflow/react";

/**
 * Represents the status of a node in the pipeline
 */
export type NodeStatus =
	| "loading"
	| "success"
	| "error"
	| "initial"
	| undefined;

/**
 * Represents a node in the pipeline
 */
export interface PipelineNode extends Node {
	id: string;
	nut_type: string;
	description?: string;
	input_types: string[];
	output_types: string[];
}

/**
 * Represents an edge in the pipeline
 */
export interface PipelineEdge extends Edge {
	source: string;
	target: string;
	data?: EdgeData;
}

/**
 * Data associated with an edge
 */
export interface EdgeData {
	status?: string;
	isRunning?: boolean;
	label?: string;
	source?: string;
	target?: string;
	sourceStatus?: NodeStatus;
	sourceIsProcessing?: boolean;
	targetStatus?: NodeStatus;
	targetIsProcessing?: boolean;
	[key: string]: unknown;
}

/**
 * Represents the structure of a pipeline
 */
export interface PipelineStructure {
	nodes: PipelineNode[];
	edges: PipelineEdge[];
}

/**
 * Result of validating a pipeline
 */
export interface ValidationResult {
	is_valid: boolean;
	errors: string[];
	pipeline?: any;
}

/**
 * Position of a menu in the UI
 */
export interface MenuPosition {
	left?: number;
	top?: number;
	right?: number;
	bottom?: number;
}

/**
 * Node types supported by the application
 */
export enum NodeType {
	DEFAULT = "default",
	INPUT = "input",
	OUTPUT = "output",
	TOOL_CUSTOM = "toolCustom",
	HANDLE_CUSTOM = "handleCustom",
	INDICATOR = "indicator",
	TRANSFORM = "transform",
	OPERATOR = "operator",
}

export const NutTypes = [
	"default",
	"input",
	"output",
	"toolCustom",
	"handleCustom",
	"indicator",
	"transform",
	"operator",
];

/**
 * Edge types supported by the application
 */
export enum EdgeType {
	CUSTOM_EDGE = "custom-edge",
}

/**
 * Layout direction options
 */
export enum LayoutDirection {
	HORIZONTAL = "LR",
	VERTICAL = "TB",
}
