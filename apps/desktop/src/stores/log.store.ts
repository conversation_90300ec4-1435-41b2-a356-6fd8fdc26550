"use client";

import { create } from "zustand";
import { devtools } from "zustand/middleware";

export type LogLevel = "info" | "warn" | "error" | "debug" | "success";
export type LogCategory =
	| "pipeline"
	| "node"
	| "execution"
	| "system"
	| "performance"
	| "user";

export interface LogEntry {
	id: string;
	timestamp: Date;
	level: LogLevel;
	message: string;
	details?: Record<string, unknown>;
	source?: string;
	category?: LogCategory;
	nodeId?: string;
	executionId?: string;
	duration?: number;
	metrics?: {
		memoryUsage?: number;
		cpuUsage?: number;
		executionTime?: number;
		[key: string]: unknown;
	};
}

export interface ExecutionMetrics {
	totalExecutionTime: number;
	nodesExecuted: number;
	nodesSucceeded: number;
	nodesFailed: number;
	averageNodeExecutionTime: number;
	longestExecutingNode?: {
		nodeId: string;
		duration: number;
	};
	errorRate: number;
}

interface LoggerState {
	logs: LogEntry[];
	isVisible: boolean;
	maxLogs: number;
	currentExecutionId?: string;
	executionMetrics: Record<string, ExecutionMetrics>;
	filters: {
		level?: LogLevel;
		category?: LogCategory;
		source?: string;
		nodeId?: string;
		executionId?: string;
	};
}

interface LoggerAction {
	addLog: (log: Omit<LogEntry, "id" | "timestamp">) => void;
	clearLogs: () => void;
	clearLogsByCategory: (category: LogCategory) => void;
	clearLogsByExecution: (executionId: string) => void;

	toggleLoggerView: () => void;
	setLoggerView: (val: boolean) => void;
	removeLog: (id: string) => void;

	// Execution tracking
	startExecution: (executionId: string) => void;
	endExecution: (executionId: string) => void;
	updateExecutionMetrics: (
		executionId: string,
		metrics: Partial<ExecutionMetrics>,
	) => void;
	getExecutionMetrics: (executionId: string) => ExecutionMetrics | undefined;

	// Filtering
	setFilters: (filters: Partial<LoggerState["filters"]>) => void;
	clearFilters: () => void;

	// Export functionality
	exportLogs: (format: "json" | "csv" | "txt") => string;
	exportExecutionReport: (executionId: string) => string;
}

const initialState: LoggerState = {
	logs: [],
	isVisible: false,
	maxLogs: 1000,
	executionMetrics: {},
	filters: {},
};

export const useLoggerStore = create<LoggerState & LoggerAction>()(
	devtools((set, get) => ({
		...initialState,

		addLog: (log: Omit<LogEntry, "id" | "timestamp">) => {
			const newLog: LogEntry = {
				...log,
				id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
				timestamp: new Date(),
			};
			const updatedLogs = [newLog, ...get().logs].slice(0, get().maxLogs);
			set({ logs: updatedLogs });
		},
		clearLogs: () => set({ logs: [] }),
		clearLogsByCategory: (category: LogCategory) =>
			set({ logs: get().logs.filter((log) => log.category !== category) }),
		clearLogsByExecution: (executionId: string) =>
			set({
				logs: get().logs.filter((log) => log.executionId !== executionId),
			}),

		toggleLoggerView: () => set({ isVisible: !get().isVisible }),
		setLoggerView: (val: boolean) => set({ isVisible: val }),
		removeLog: (id: string) =>
			set({ logs: get().logs.filter((log) => log.id !== id) }),

		// Execution tracking
		startExecution: (executionId: string) => {
			set({ currentExecutionId: executionId });
			set((state) => ({
				executionMetrics: {
					...state.executionMetrics,
					[executionId]: {
						totalExecutionTime: 0,
						nodesExecuted: 0,
						nodesSucceeded: 0,
						nodesFailed: 0,
						averageNodeExecutionTime: 0,
						errorRate: 0,
					},
				},
			}));
		},

		endExecution: (executionId: string) => {
			if (get().currentExecutionId === executionId) {
				set({ currentExecutionId: undefined });
			}
		},

		updateExecutionMetrics: (
			executionId: string,
			metrics: Partial<ExecutionMetrics>,
		) => {
			set((state) => ({
				executionMetrics: {
					...state.executionMetrics,
					[executionId]: {
						...state.executionMetrics[executionId],
						...metrics,
					},
				},
			}));
		},

		getExecutionMetrics: (executionId: string) => {
			return get().executionMetrics[executionId];
		},

		// Filtering
		setFilters: (filters: Partial<LoggerState["filters"]>) => {
			set((state) => ({
				filters: { ...state.filters, ...filters },
			}));
		},

		clearFilters: () => set({ filters: {} }),

		// Export functionality
		exportLogs: (format: "json" | "csv" | "txt") => {
			const logs = get().logs;
			switch (format) {
				case "json":
					return JSON.stringify(logs, null, 2);
				case "csv": {
					const headers = [
						"timestamp",
						"level",
						"category",
						"source",
						"nodeId",
						"message",
						"duration",
					];
					const csvRows = [headers.join(",")];
					logs.forEach((log) => {
						const row = [
							log.timestamp.toISOString(),
							log.level,
							log.category || "",
							log.source || "",
							log.nodeId || "",
							`"${log.message.replace(/"/g, '""')}"`,
							log.duration || "",
						];
						csvRows.push(row.join(","));
					});
					return csvRows.join("\n");
				}
				case "txt":
					return logs
						.map(
							(log) =>
								`[${log.timestamp.toISOString()}] ${log.level.toUpperCase()} ${log.category ? `[${log.category}]` : ""} ${log.source ? `[${log.source}]` : ""} ${log.nodeId ? `[${log.nodeId}]` : ""}: ${log.message}${log.duration ? ` (${log.duration}ms)` : ""}`,
						)
						.join("\n");
				default:
					return "";
			}
		},

		exportExecutionReport: (executionId: string) => {
			const metrics = get().executionMetrics[executionId];
			const executionLogs = get().logs.filter(
				(log) => log.executionId === executionId,
			);

			if (!metrics) return "";

			const report = {
				executionId,
				metrics,
				logs: executionLogs,
				summary: {
					totalLogs: executionLogs.length,
					errorLogs: executionLogs.filter((log) => log.level === "error")
						.length,
					warningLogs: executionLogs.filter((log) => log.level === "warn")
						.length,
					successLogs: executionLogs.filter((log) => log.level === "success")
						.length,
				},
			};

			return JSON.stringify(report, null, 2);
		},
	})),
);

// Enhanced logging functions with pipeline support
interface LogOptions {
	category?: LogCategory;
	nodeId?: string;
	executionId?: string;
	duration?: number;
	metrics?: LogEntry["metrics"];
}

const log = (
	level: LogLevel,
	message: string,
	details?: Record<string, unknown>,
	source?: string,
	options?: LogOptions,
) => {
	useLoggerStore.getState().addLog({
		level,
		message,
		details,
		source,
		...options,
	});
};

// Basic logging functions
export const info = (
	message: string,
	details?: Record<string, unknown>,
	source?: string,
	options?: LogOptions,
) => {
	log("info", message, details, source, options);
};

export const warn = (
	message: string,
	details?: Record<string, unknown>,
	source?: string,
	options?: LogOptions,
) => {
	log("warn", message, details, source, options);
};

export const error = (
	message: string,
	details?: Record<string, unknown>,
	source?: string,
	options?: LogOptions,
) => {
	log("error", message, details, source, options);
};

export const debug = (
	message: string,
	details?: Record<string, unknown>,
	source?: string,
	options?: LogOptions,
) => {
	log("debug", message, details, source, options);
};

export const success = (
	message: string,
	details?: Record<string, unknown>,
	source?: string,
	options?: LogOptions,
) => {
	log("success", message, details, source, options);
};

// Pipeline-specific logging functions
export const logPipelineStart = (executionId: string, nodeCount: number) => {
	useLoggerStore.getState().startExecution(executionId);
	info(
		`Pipeline execution started with ${nodeCount} nodes`,
		{ nodeCount },
		"Pipeline",
		{
			category: "pipeline",
			executionId,
		},
	);
};

export const logPipelineEnd = (
	executionId: string,
	duration: number,
	successCount: number,
	errorCount: number,
) => {
	useLoggerStore.getState().endExecution(executionId);
	success(
		`Pipeline execution completed in ${duration}ms`,
		{
			duration,
			successCount,
			errorCount,
			successRate: (successCount / (successCount + errorCount)) * 100,
		},
		"Pipeline",
		{
			category: "pipeline",
			executionId,
			duration,
		},
	);
};

export const logNodeStart = (
	nodeId: string,
	executionId: string,
	nodeType?: string,
) => {
	info(`Node execution started: ${nodeId}`, { nodeType }, "Node", {
		category: "node",
		nodeId,
		executionId,
	});
};

export const logNodeSuccess = (
	nodeId: string,
	executionId: string,
	duration: number,
	result?: Record<string, unknown>,
) => {
	success(`Node executed successfully: ${nodeId}`, { result }, "Node", {
		category: "node",
		nodeId,
		executionId,
		duration,
	});
};

export const logNodeError = (
	nodeId: string,
	executionId: string,
	duration: number,
	errorMessage: string,
	errorDetails?: Record<string, unknown>,
) => {
	error(
		`Node execution failed: ${nodeId} - ${errorMessage}`,
		{ errorDetails },
		"Node",
		{
			category: "node",
			nodeId,
			executionId,
			duration,
		},
	);
};

export const logPerformance = (
	operation: string,
	duration: number,
	metrics?: Record<string, unknown>,
) => {
	info(
		`Performance: ${operation} completed in ${duration}ms`,
		metrics,
		"Performance",
		{
			category: "performance",
			duration,
			metrics: {
				executionTime: duration,
				...metrics,
			},
		},
	);
};
