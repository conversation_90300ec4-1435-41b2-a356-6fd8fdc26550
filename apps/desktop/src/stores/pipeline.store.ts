import { create } from "zustand";
import { devtools, persist, subscribeWithSelector } from "zustand/middleware";
import { applyEdgeChanges, applyNodeChanges } from "@xyflow/react";
import type {
	OnEdgesChange,
	Edge,
	Node,
	OnNodesChange,
	ReactFlowInstance,
	FitView,
} from "@xyflow/react";
import type {
	PipelineStructure,
	ValidationResult,
} from "../types/pipeline.types";
import { LayoutDirection } from "../types/pipeline.types";
import { getLayoutedElements } from "../utils/view.utils";
import { useShallow } from "zustand/shallow";
import { useNodeStore } from "./node.store";
import { info, warn, error, debug, success } from "./log.store";

interface PipelineState {
	fileName: string; // json file with pipeline
	// Pipeline data
	nodes: Node[];
	edges: Edge[];
	pipelineStructure: PipelineStructure | null;
	validationResult: ValidationResult | null;

	// UI state
	isLoading: boolean;
	selectedNodeId: string | null;
	layoutDirection: LayoutDirection;
	reactFlowInstance: ReactFlowInstance | null;

	// Pipeline execution state
	isPipelineRunning: boolean;
	currentExecutingNodeId: string | null;
}

interface PipelineActions {
	setPipelineFile: (file: string) => void;
	setFlowInstance: (flow: ReactFlowInstance) => void;
	fitViewNode: (nodeId: string) => void;
	fitView: FitView;
	// Node actions
	setNodes: (update: Node[] | ((oldState: Node[]) => Node[])) => void;
	updateNode: (
		nodeId: string,
		nodeInfo: Partial<Omit<Node, "data" | "id">>,
		nodeData: Node["data"],
	) => void;
	addNode: (node: Node) => void;
	removeNode: (nodeId: string) => void;
	onNodesChange: OnNodesChange;

	// Edge actions
	setEdges: (update: Edge[] | ((oldState: Edge[]) => Edge[])) => void;
	updateEdge: (edgeId: string, data: any) => void;
	addEdge: (edge: Edge) => void;
	removeEdge: (edgeId: string) => void;
	onEdgesChange: OnEdgesChange;

	// Pipeline actions
	setPipelineStructure: (structure: PipelineStructure | null) => void;
	setValidationResult: (result: ValidationResult | null) => void;

	// Pipeline execution actions
	startPipelineExecution: () => void;
	stopPipelineExecution: () => void;
	setCurrentExecutingNodeId: (nodeId: string | null) => void;

	// Layout actions
	setLayoutDirection: (direction: LayoutDirection) => void;
	applyLayout: () => void;

	// Selection actions
	setSelectedNodeId: (nodeId: string | null) => void;

	// Loading state
	setIsLoading: (isLoading: boolean) => void;

	// Reset
	resetState: () => void;
}

// Initial state
const initialState: PipelineState = {
	fileName: "",
	nodes: [],
	edges: [],
	pipelineStructure: null,
	validationResult: null,
	isLoading: false,
	selectedNodeId: null,
	layoutDirection: LayoutDirection.HORIZONTAL,
	reactFlowInstance: null,
	isPipelineRunning: false,
	currentExecutingNodeId: null,
};

export const usePipelineStore = create<PipelineState & PipelineActions>()(
	devtools(
		persist(
			subscribeWithSelector((set, get) => ({
				...initialState,

				setPipelineFile: (file: string) => set({ fileName: file }),

				setFlowInstance: (flow) => set({ reactFlowInstance: flow }),

				fitViewNode: (nodeId) => {
					if (
						get().reactFlowInstance &&
						get().nodes.find((n) => n.id === nodeId)
					) {
						get().reactFlowInstance?.fitView({ nodes: [{ id: nodeId }] });
					}
				},
				fitView: (params) =>
					new Promise(() => get().reactFlowInstance?.fitView(params)),

				// Node actions
				setNodes: (change) => {
					const newChange =
						typeof change === "function" ? change(get().nodes) : change;
					const oldCount = get().nodes.length;
					const newCount = newChange.length;

					if (newCount !== oldCount) {
						info(
							`Pipeline nodes updated: ${oldCount} → ${newCount}`,
							{
								oldCount,
								newCount,
								delta: newCount - oldCount,
							},
							"PipelineStore",
							{
								category: "pipeline",
							},
						);
					}

					set({ nodes: newChange });
				},
				updateNode: (nodeId, nodeInfo, nodeData) =>
					set((state) => ({
						nodes: state.nodes.map((node) =>
							node.id === nodeId
								? { ...node, ...nodeInfo, data: { ...node.data, ...nodeData } }
								: node,
						),
					})),
				addNode: (node) =>
					set((state) => ({
						nodes: [...state.nodes, node],
					})),
				removeNode: (nodeId) =>
					set((state) => ({
						nodes: state.nodes.filter((node) => node.id !== nodeId),
						edges: state.edges.filter(
							(edge) => edge.source !== nodeId && edge.target !== nodeId,
						),
					})),
				onNodesChange: (changes) =>
					set({ nodes: applyNodeChanges(changes, get().nodes) }),

				// Edge actions
				setEdges: (change) => {
					const newChange =
						typeof change === "function" ? change(get().edges) : change;
					set({ edges: newChange });
				},
				updateEdge: (edgeId, data) =>
					set((state) => ({
						edges: state.edges.map((edge) =>
							edge.id === edgeId
								? { ...edge, data: { ...edge.data, ...data } }
								: edge,
						),
					})),
				addEdge: (edge) =>
					set((state) => ({
						edges: [...state.edges, edge],
					})),
				removeEdge: (edgeId) =>
					set((state) => ({
						edges: state.edges.filter((edge) => edge.id !== edgeId),
					})),
				onEdgesChange: (changes) =>
					set({ edges: applyEdgeChanges(changes, get().edges) }),

				// Pipeline actions
				setPipelineStructure: (structure) =>
					set({ pipelineStructure: structure }),
				setValidationResult: (result) => set({ validationResult: result }),

				// Pipeline execution actions
				startPipelineExecution: () => {
					const { nodes, edges } = get();
					set({ isPipelineRunning: true });

					// Find root nodes (nodes with no incoming edges)
					const rootNodes = nodes.filter((node) => {
						return !edges.some((edge) => edge.target === node.id);
					});

					// Reset all node statuses
					nodes.forEach((node) => {
						const { resetNodeStatus } = useNodeStore.getState();
						resetNodeStatus(node.id);
					});

					// Start execution from root nodes
					rootNodes.forEach((node) => {
						const { startProcessing } = useNodeStore.getState();
						set({ currentExecutingNodeId: node.id });
						startProcessing(node.id);

						// Simulate processing time
						const processingTime = 1500 + Math.random() * 1000;
						const willSucceed = Math.random() > 0.2; // 80% success rate

						setTimeout(() => {
							const { stopProcessing, startProcessing } =
								useNodeStore.getState();
							stopProcessing(node.id, willSucceed ? "success" : "error");

							// If successful, process connected nodes
							if (willSucceed) {
								const connectedEdges = get().edges.filter(
									(edge) => edge.source === node.id,
								);
								connectedEdges.forEach((edge) => {
									const nextNodeId = edge.target;
									setTimeout(() => {
										startProcessing(nextNodeId);
									}, 500);
								});
							} else {
								// If this was the last executing node, mark pipeline as stopped
								if (get().currentExecutingNodeId === node.id) {
									set({
										isPipelineRunning: false,
										currentExecutingNodeId: null,
									});
								}
							}
						}, processingTime);
					});

					// If no root nodes, stop execution
					if (rootNodes.length === 0) {
						set({ isPipelineRunning: false, currentExecutingNodeId: null });
					}
				},

				stopPipelineExecution: () => {
					set({ isPipelineRunning: false, currentExecutingNodeId: null });

					// Reset all processing nodes
					const { processingNodes, resetNodeStatus } = useNodeStore.getState();
					processingNodes.forEach((nodeId: string) => {
						resetNodeStatus(nodeId);
					});
				},

				setCurrentExecutingNodeId: (nodeId) =>
					set({ currentExecutingNodeId: nodeId }),

				// Layout actions
				setLayoutDirection: (direction) => set({ layoutDirection: direction }),
				applyLayout: () => {
					const { nodes, edges, layoutDirection } = get();
					const layout = getLayoutedElements(nodes, edges, {
						direction: layoutDirection,
					});
					set({
						nodes: [...layout.nodes],
						edges: [...layout.edges],
					});
				},

				// Selection actions
				setSelectedNodeId: (nodeId) => set({ selectedNodeId: nodeId }),

				// Loading state
				setIsLoading: (isLoading) => set({ isLoading }),

				// Reset
				resetState: () => set(initialState),
			})),
			{
				name: "pipeline-storage",
				partialize: (state) => ({
					// Only persist these fields
					pipelineStructure: state.pipelineStructure,
					layoutDirection: state.layoutDirection,
				}),
			},
		),
	),
);

// Selector hooks for better performance
export const useNodes = () => usePipelineStore((state) => state.nodes);
export const useEdges = () => usePipelineStore((state) => state.edges);
export const usePipelineStructure = () =>
	usePipelineStore((state) => state.pipelineStructure);
export const useValidationResult = () =>
	usePipelineStore((state) => state.validationResult);
export const useLayoutDirection = () =>
	usePipelineStore((state) => state.layoutDirection);
export const useSelectedNodeId = () =>
	usePipelineStore((state) => state.selectedNodeId);
export const usePipelineExecution = () =>
	usePipelineStore(
		useShallow((state) => ({
			isPipelineRunning: state.isPipelineRunning,
			currentExecutingNodeId: state.currentExecutingNodeId,
			startPipelineExecution: state.startPipelineExecution,
			stopPipelineExecution: state.stopPipelineExecution,
		})),
	);
