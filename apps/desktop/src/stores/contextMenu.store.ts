import { create } from "zustand";

interface MenuPosition {
	left?: number;
	top?: number;
	right?: number;
	bottom?: number;
}

interface ContextMenuState {
	id: string;
	isLocked: boolean;
	isVisible: boolean;
	position: MenuPosition;
}

interface ContextMenuActions {
	setIsLocked: (val: boolean) => void;
	setIsVisible: (val: boolean) => void;

	setNodeId: (id: string) => void;
	setPosition: (pos: MenuPosition) => void;
	setPositionValues: (
		top?: number,
		left?: number,
		bottom?: number,
		right?: number,
	) => void;
}

const useContextMenuStore = create<ContextMenuState & ContextMenuActions>(
	(set) => ({
		id: "",
		isLocked: false,
		isVisible: false,
		position: { left: 0 },

		setIsLocked: (val: boolean) => set(() => ({ isLocked: val })),
		setIsVisible: (val: boolean) => set(() => ({ isVisible: val })),

		setNodeId: (id: string) => set(() => ({ id })),
		setPosition: (pos: MenuPosition) => set(() => ({ position: pos })),
		setPositionValues: (
			top?: number,
			left?: number,
			bottom?: number,
			right?: number,
		) => set(() => ({ position: { left, top, right, bottom } })),
	}),
);

export default useContextMenuStore;
