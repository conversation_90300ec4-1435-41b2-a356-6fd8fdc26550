import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { useShallow } from "zustand/shallow";
import type { Node } from "@xyflow/react";
import type { MenuPosition } from "../types/pipeline.types";

interface UIState {
	// Context menu
	contextMenuNodeId: string;
	contextMenuPosition: MenuPosition;
	isContextMenuVisible: boolean;

	// Node editing
	editingNode: Node | null;
	isEditDialogOpen: boolean;
	isSettingsDialogOpen: boolean;

	// View settings
	zoomLevel: number;
	isDarkMode: boolean;
	showMinimap: boolean;
	showControls: boolean;

	// Sidebar
	isSidebarOpen: boolean;
	sidebarWidth: number;

	// Notifications
	notifications: Notification[];
}

interface UIActions {
	// Context menu actions
	setContextMenuNodeId: (id: string) => void;
	setContextMenuPosition: (position: MenuPosition) => void;
	showContextMenu: (id: string, position: MenuPosition) => void;
	hideContextMenu: () => void;

	// Node editing actions
	setEditingNode: (node: Node | null) => void;
	openEditDialog: (node: Node) => void;
	openSettingsDialog: () => void;
	closeEditDialog: () => void;

	// View settings actions
	setZoomLevel: (level: number) => void;
	toggleDarkMode: () => void;
	setShowMinimap: (show: boolean) => void;
	setShowControls: (show: boolean) => void;

	// Sidebar actions
	toggleSidebar: () => void;
	setSidebarWidth: (width: number) => void;

	// Notification actions
	addNotification: (notification: Notification) => void;
	removeNotification: (id: string) => void;
	clearNotifications: () => void;
}

interface Notification {
	id: string;
	type: "info" | "success" | "warning" | "error";
	message: string;
	duration?: number;
}

// Initial state
const initialState: UIState = {
	contextMenuNodeId: "",
	contextMenuPosition: { left: 0, top: 0 },
	isContextMenuVisible: false,

	editingNode: null,
	isEditDialogOpen: false,
	isSettingsDialogOpen: false,

	zoomLevel: 1,
	isDarkMode: false,
	showMinimap: true,
	showControls: true,

	isSidebarOpen: true,
	sidebarWidth: 250,

	notifications: [],
};

export const useUIStore = create<UIState & UIActions>()(
	devtools((set, get) => ({
		...initialState,

		// Context menu actions
		setContextMenuNodeId: (id) => set({ contextMenuNodeId: id }),
		setContextMenuPosition: (position) =>
			set({ contextMenuPosition: position }),
		showContextMenu: (id, position) =>
			set({
				contextMenuNodeId: id,
				contextMenuPosition: position,
				isContextMenuVisible: true,
			}),
		hideContextMenu: () =>
			set({
				contextMenuNodeId: "",
				isContextMenuVisible: false,
			}),

		// Node editing actions
		setEditingNode: (node) => set({ editingNode: node }),
		openEditDialog: (node) =>
			set({
				editingNode: node,
				isEditDialogOpen: true,
			}),
		closeEditDialog: () =>
			set({
				editingNode: null,
				isEditDialogOpen: false,
				isSettingsDialogOpen: false,
			}),

		// View settings actions
		setZoomLevel: (level) => set({ zoomLevel: level }),
		toggleDarkMode: () =>
			set((state) => ({
				isDarkMode: !state.isDarkMode,
			})),
		setShowMinimap: (show) => set({ showMinimap: show }),
		setShowControls: (show) => set({ showControls: show }),

		// Sidebar actions
		toggleSidebar: () =>
			set((state) => ({
				isSidebarOpen: !state.isSidebarOpen,
			})),
		setSidebarWidth: (width) => set({ sidebarWidth: width }),

		// Notification actions
		addNotification: (notification) =>
			set((state) => ({
				notifications: [...state.notifications, notification],
			})),
		removeNotification: (id) =>
			set((state) => ({
				notifications: state.notifications.filter((n) => n.id !== id),
			})),
		clearNotifications: () => set({ notifications: [] }),

		openSettingsDialog: () => set({ isSettingsDialogOpen: true }),
	})),
);

// Selector hooks for better performance
export const useContextMenu = () =>
	useUIStore(
		useShallow((state) => ({
			nodeId: state.contextMenuNodeId,
			position: state.contextMenuPosition,
			isVisible: state.isContextMenuVisible,
			show: state.showContextMenu,
			hide: state.hideContextMenu,
		})),
	);

export const useEditDialog = () =>
	useUIStore(
		useShallow((state) => ({
			node: state.editingNode,
			isOpen: state.isEditDialogOpen,
			open: state.openEditDialog,
			close: state.closeEditDialog,
		})),
	);

export const useViewSettings = () =>
	useUIStore(
		useShallow((state) => ({
			zoomLevel: state.zoomLevel,
			isDarkMode: state.isDarkMode,
			showMinimap: state.showMinimap,
			showControls: state.showControls,
			setZoomLevel: state.setZoomLevel,
			toggleDarkMode: state.toggleDarkMode,
			setShowMinimap: state.setShowMinimap,
			setShowControls: state.setShowControls,
		})),
	);

export const useSidebar = () =>
	useUIStore(
		useShallow((state) => ({
			isOpen: state.isSidebarOpen,
			width: state.sidebarWidth,
			toggle: state.toggleSidebar,
			setWidth: state.setSidebarWidth,
		})),
	);

export const useNotifications = () =>
	useUIStore(
		useShallow((state) => ({
			notifications: state.notifications,
			add: state.addNotification,
			remove: state.removeNotification,
			clear: state.clearNotifications,
		})),
	);
