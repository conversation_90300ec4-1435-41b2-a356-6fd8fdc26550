import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { useShallow } from "zustand/shallow";
import type { Edge, Node } from "@xyflow/react";
import type { NodeStatus } from "../types/pipeline.types";
import { useNodeStore } from "./node.store";
import { usePipelineStore } from "./pipeline.store";
import {
	logPipelineStart,
	logPipelineEnd,
	logNodeStart,
	logNodeSuccess,
	logNodeError,
	logPerformance,
	info,
	warn,
	error,
	debug,
} from "./log.store";

/**
 * Represents the execution state of a node
 */
export interface NodeExecutionState {
	id: string;
	status: NodeStatus;
	startTime?: number;
	endTime?: number;
	duration?: number;
	error?: string;
	inputs: {
		nodeId: string;
		status: NodeStatus;
	}[];
	outputs: string[];
	isWaitingForInputs: boolean;
	isExecuted: boolean;
}

/**
 * Represents the execution state of the pipeline
 */
export interface ExecutionState {
	// Execution status
	isRunning: boolean;
	isPaused: boolean;
	startTime?: number;
	endTime?: number;
	duration?: number;
	currentExecutionId?: string;

	// Node execution tracking
	nodeExecutionStates: Record<string, NodeExecutionState>;
	executionQueue: string[];
	currentlyExecutingNodes: string[];
	executedNodes: string[];
	failedNodes: string[];

	// Execution statistics
	successCount: number;
	errorCount: number;
	totalNodesCount: number;
	progress: number;
}

interface ExecutionActions {
	// Pipeline execution control
	startExecution: () => void;
	pauseExecution: () => void;
	resumeExecution: () => void;
	stopExecution: () => void;
	resetExecution: () => void;

	// Node execution control
	executeNode: (nodeId: string) => Promise<void>;
	markNodeAsExecuted: (
		nodeId: string,
		status: NodeStatus,
		error?: string,
	) => void;

	// Queue management
	addToExecutionQueue: (nodeIds: string[]) => void;
	removeFromExecutionQueue: (nodeId: string) => void;
	processExecutionQueue: () => void;

	// Dependency management
	checkNodeDependencies: (nodeId: string) => boolean;
	getNodeInputs: (nodeId: string) => string[];
	getNodeOutputs: (nodeId: string) => string[];

	// Execution state management
	updateNodeExecutionState: (
		nodeId: string,
		updates: Partial<Omit<NodeExecutionState, "id">>,
	) => void;

	// Utility functions
	getExecutableRootNodes: () => string[];
	getNextExecutableNodes: () => string[];
	calculateProgress: () => number;
}

// Initial state
const initialState: ExecutionState = {
	isRunning: false,
	isPaused: false,
	currentExecutionId: undefined,

	nodeExecutionStates: {},
	executionQueue: [],
	currentlyExecutingNodes: [],
	executedNodes: [],
	failedNodes: [],

	successCount: 0,
	errorCount: 0,
	totalNodesCount: 0,
	progress: 0,
};

export const useExecutionStore = create<ExecutionState & ExecutionActions>()(
	devtools((set, get) => ({
		...initialState,

		// Pipeline execution control
		startExecution: () => {
			const { nodes, edges } = usePipelineStore.getState();
			const { resetNodeStatus } = useNodeStore.getState();

			// Generate execution ID
			const executionId = `exec-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

			// Log pipeline start
			logPipelineStart(executionId, nodes.length);
			info(
				`Starting pipeline execution with ${nodes.length} nodes and ${edges.length} edges`,
				{
					nodeCount: nodes.length,
					edgeCount: edges.length,
					executionId,
				},
				"ExecutionStore",
				{
					category: "execution",
					executionId,
				},
			);

			// Reset all node statuses
			nodes.forEach((node) => {
				resetNodeStatus(node.id);
			});

			// Initialize execution state
			const nodeExecutionStates: Record<string, NodeExecutionState> = {};

			// Build node execution states with dependency information
			nodes.forEach((node) => {
				// Find incoming edges (inputs)
				const inputs = edges
					.filter((edge) => edge.target === node.id)
					.map((edge) => ({
						nodeId: edge.source,
						status: "initial" as NodeStatus,
					}));

				// Find outgoing edges (outputs)
				const outputs = edges
					.filter((edge) => edge.source === node.id)
					.map((edge) => edge.target);

				// Create node execution state
				nodeExecutionStates[node.id] = {
					id: node.id,
					status: "initial",
					inputs,
					outputs,
					isWaitingForInputs: inputs.length > 0,
					isExecuted: false,
				};
			});

			// Find root nodes (nodes with no inputs)
			const rootNodes = nodes
				.filter((node) => {
					const state = nodeExecutionStates[node.id];
					return state && state.inputs.length === 0;
				})
				.map((node) => node.id);

			// Start execution
			set({
				isRunning: true,
				isPaused: false,
				startTime: Date.now(),
				currentExecutionId: executionId,
				nodeExecutionStates,
				executionQueue: [...rootNodes],
				currentlyExecutingNodes: [],
				executedNodes: [],
				failedNodes: [],
				successCount: 0,
				errorCount: 0,
				totalNodesCount: nodes.length,
				progress: 0,
			});

			// Process the queue
			setTimeout(() => {
				get().processExecutionQueue();
			}, 0);
		},

		pauseExecution: () => {
			set({ isPaused: true });
		},

		resumeExecution: () => {
			set({ isPaused: false });
			get().processExecutionQueue();
		},

		stopExecution: () => {
			const { resetNodeStatus } = useNodeStore.getState();
			const { currentlyExecutingNodes } = get();

			// Reset status of currently executing nodes
			currentlyExecutingNodes.forEach((nodeId) => {
				resetNodeStatus(nodeId);
			});

			set({
				isRunning: false,
				isPaused: false,
				endTime: Date.now(),
				currentlyExecutingNodes: [],
				executionQueue: [],
			});

			// Calculate duration if we have start time
			if (get().startTime) {
				set({ duration: Date.now() - get().startTime! });
			}
		},

		resetExecution: () => {
			const { nodes } = usePipelineStore.getState();
			const { resetNodeStatus } = useNodeStore.getState();

			// Reset all node statuses
			nodes.forEach((node) => {
				resetNodeStatus(node.id);
			});

			set(initialState);
		},

		// Node execution control
		executeNode: async (nodeId: string) => {
			const { startProcessing, stopProcessing } = useNodeStore.getState();
			const {
				updateNodeExecutionState,
				markNodeAsExecuted,
				currentExecutionId,
			} = get();

			// Check if node is already executing
			if (get().currentlyExecutingNodes.includes(nodeId)) {
				warn(
					`Node ${nodeId} is already executing`,
					{ nodeId },
					"ExecutionStore",
					{
						category: "execution",
						nodeId,
						executionId: currentExecutionId,
					},
				);
				return;
			}

			// Log node start
			logNodeStart(nodeId, currentExecutionId || "unknown");

			// Add node to currently executing nodes
			set((state) => ({
				currentlyExecutingNodes: [...state.currentlyExecutingNodes, nodeId],
			}));

			// Update node execution state
			updateNodeExecutionState(nodeId, {
				status: "loading",
				startTime: Date.now(),
			});

			// Start processing the node
			startProcessing(nodeId);

			const startTime = Date.now();

			try {
				// Simulate node execution (replace with actual execution logic)
				const executionTime = 1500 + Math.random() * 2000;
				const willSucceed = Math.random() > 0.2; // 80% success rate

				await new Promise((resolve) => setTimeout(resolve, executionTime));

				if (!willSucceed) {
					throw new Error(`Node ${nodeId} execution failed`);
				}

				const duration = Date.now() - startTime;

				// Log successful execution
				logNodeSuccess(nodeId, currentExecutionId || "unknown", duration, {
					executionTime,
					actualDuration: duration,
				});

				// Mark node as executed with success status
				markNodeAsExecuted(nodeId, "success");
				stopProcessing(nodeId, "success");
			} catch (error) {
				const duration = Date.now() - startTime;
				const errorMessage =
					error instanceof Error ? error.message : String(error);

				// Log failed execution
				logNodeError(
					nodeId,
					currentExecutionId || "unknown",
					duration,
					errorMessage,
					{
						error:
							error instanceof Error
								? {
										name: error.name,
										message: error.message,
										stack: error.stack,
									}
								: error,
					},
				);

				// Mark node as executed with error status
				markNodeAsExecuted(nodeId, "error", errorMessage);
				stopProcessing(nodeId, "error");
			}
		},

		markNodeAsExecuted: (
			nodeId: string,
			status: NodeStatus,
			error?: string,
		) => {
			const { updateNodeExecutionState, processExecutionQueue } = get();
			const endTime = Date.now();
			const startTime = get().nodeExecutionStates[nodeId]?.startTime || endTime;

			// Update node execution state
			updateNodeExecutionState(nodeId, {
				status,
				endTime,
				duration: endTime - startTime,
				error,
				isWaitingForInputs: status !== "success",
				isExecuted: true,
			});

			// Update execution tracking
			set((state) => {
				const newState: Partial<ExecutionState> = {
					currentlyExecutingNodes: state.currentlyExecutingNodes.filter(
						(id) => id !== nodeId,
					),
					executedNodes: [...state.executedNodes, nodeId],
					successCount:
						status === "success" ? state.successCount + 1 : state.successCount,
					errorCount:
						status === "error" ? state.errorCount + 1 : state.errorCount,
				};

				// Add to failed nodes if error
				if (status === "error") {
					newState.failedNodes = [...state.failedNodes, nodeId];
				}

				return newState;
			});

			// Update progress
			set((state) => ({
				progress: (state.executedNodes.length / state.totalNodesCount) * 100,
			}));

			// If node executed successfully, add its outputs to the execution queue
			if (status === "success") {
				const outputs = get().nodeExecutionStates[nodeId]?.outputs || [];
				const nextNodes = outputs.filter((outputNodeId) => {
					// Check if all inputs of the output node are executed
					return get().checkNodeDependencies(outputNodeId);
				});

				if (nextNodes.length > 0) {
					get().addToExecutionQueue(nextNodes);
				}
			}

			// If node failed, stop execution if configured to do so
			if (status === "error") {
				// For now, we'll continue execution of other branches
				// In a real implementation, you might want to add a configuration option
				// to stop execution on first error
			}

			// Process the queue
			setTimeout(() => {
				processExecutionQueue();
			}, 0);
		},

		// Queue management
		addToExecutionQueue: (nodeIds: string[]) => {
			set((state) => ({
				executionQueue: [...state.executionQueue, ...nodeIds],
			}));
		},

		removeFromExecutionQueue: (nodeId: string) => {
			set((state) => ({
				executionQueue: state.executionQueue.filter((id) => id !== nodeId),
			}));
		},

		processExecutionQueue: () => {
			const { isRunning, isPaused, executionQueue, currentlyExecutingNodes } =
				get();

			// Don't process if not running or paused
			if (!isRunning || isPaused) {
				return;
			}

			// Get next nodes to execute (up to a maximum parallel execution limit)
			const maxParallelExecutions = 5; // Configurable
			const availableSlots =
				maxParallelExecutions - currentlyExecutingNodes.length;

			if (availableSlots <= 0 || executionQueue.length === 0) {
				// Check if we're done
				if (
					currentlyExecutingNodes.length === 0 &&
					executionQueue.length === 0
				) {
					// All nodes executed
					const endTime = Date.now();
					const startTime = get().startTime || endTime;
					const duration = endTime - startTime;
					const { successCount, errorCount, currentExecutionId } = get();

					// Log pipeline completion
					if (currentExecutionId) {
						logPipelineEnd(
							currentExecutionId,
							duration,
							successCount,
							errorCount,
						);
					}

					set({
						isRunning: false,
						endTime,
						duration,
						currentExecutionId: undefined,
					});
				}
				return;
			}

			// Take nodes from the queue
			const nodesToExecute = executionQueue.slice(0, availableSlots);

			// Remove nodes from queue
			set((state) => ({
				executionQueue: state.executionQueue.slice(availableSlots),
			}));

			// Execute nodes
			nodesToExecute.forEach((nodeId) => {
				get().executeNode(nodeId);
			});
		},

		// Dependency management
		checkNodeDependencies: (nodeId: string) => {
			const nodeState = get().nodeExecutionStates[nodeId];

			if (!nodeState) {
				return false;
			}

			// If node has no inputs, it's ready to execute
			if (nodeState.inputs.length === 0) {
				return true;
			}

			// Check if all inputs are executed successfully
			return nodeState.inputs.every((input) => {
				const inputNodeState = get().nodeExecutionStates[input.nodeId];
				return (
					inputNodeState &&
					inputNodeState.status === "success" &&
					inputNodeState.isExecuted
				);
			});
		},

		getNodeInputs: (nodeId: string) => {
			return (
				get().nodeExecutionStates[nodeId]?.inputs.map(
					(input) => input.nodeId,
				) || []
			);
		},

		getNodeOutputs: (nodeId: string) => {
			return get().nodeExecutionStates[nodeId]?.outputs || [];
		},

		// Execution state management
		updateNodeExecutionState: (
			nodeId: string,
			updates: Partial<Omit<NodeExecutionState, "id">>,
		) => {
			set((state) => {
				const currentNodeState = state.nodeExecutionStates[nodeId] || {
					id: nodeId,
					status: "initial",
					inputs: [],
					outputs: [],
					isWaitingForInputs: false,
					isExecuted: false,
				};

				return {
					nodeExecutionStates: {
						...state.nodeExecutionStates,
						[nodeId]: {
							...currentNodeState,
							...updates,
						},
					},
				};
			});
		},

		// Utility functions
		getExecutableRootNodes: () => {
			const { nodes, edges } = usePipelineStore.getState();

			// Find nodes with no incoming edges
			return nodes
				.filter((node) => !edges.some((edge) => edge.target === node.id))
				.map((node) => node.id);
		},

		getNextExecutableNodes: () => {
			const { executedNodes, nodeExecutionStates } = get();
			const { edges } = usePipelineStore.getState();

			// Find nodes that have all their inputs executed
			const potentialNodes = edges
				.filter((edge) => executedNodes.includes(edge.source))
				.map((edge) => edge.target);

			// Filter to nodes that have all inputs satisfied
			return [...new Set(potentialNodes)].filter((nodeId) => {
				return get().checkNodeDependencies(nodeId);
			});
		},

		calculateProgress: () => {
			const { executedNodes, totalNodesCount } = get();
			return totalNodesCount > 0
				? (executedNodes.length / totalNodesCount) * 100
				: 0;
		},
	})),
);

// Selector hooks for better performance
export const useExecutionStatus = () =>
	useExecutionStore(
		useShallow((state) => ({
			isRunning: state.isRunning,
			isPaused: state.isPaused,
			progress: state.progress,
			successCount: state.successCount,
			errorCount: state.errorCount,
			totalNodesCount: state.totalNodesCount,
			startTime: state.startTime,
			endTime: state.endTime,
			duration: state.duration,
		})),
	);

export const useNodeExecutionState = (nodeId: string) =>
	useExecutionStore((state) => state.nodeExecutionStates[nodeId]);

export const useExecutionControls = () =>
	useExecutionStore(
		useShallow((state) => ({
			startExecution: state.startExecution,
			pauseExecution: state.pauseExecution,
			resumeExecution: state.resumeExecution,
			stopExecution: state.stopExecution,
			resetExecution: state.resetExecution,
		})),
	);

export const useExecutionProgress = () =>
	useExecutionStore(
		useShallow((state) => ({
			executedNodes: state.executedNodes,
			failedNodes: state.failedNodes,
			currentlyExecutingNodes: state.currentlyExecutingNodes,
			progress: state.progress,
			successCount: state.successCount,
			errorCount: state.errorCount,
			totalNodesCount: state.totalNodesCount,
		})),
	);
