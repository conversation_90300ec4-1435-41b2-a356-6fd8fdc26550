<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/pleros.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Helios</title>
    <style>
        body {
            overflow: hidden;
            background-color: #f6f6f6;
        }

        .splash_container {
            width: 100dvw;
            height: 100dvh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }


        .logo {
            animation: pulse-rotate 2s ease-in-out infinite;
            filter: drop-shadow(0 0 8px rgba(42, 67, 233, 0.3));
            width: 120px;
            height: 120px;
        }

        @keyframes pulse-rotate {
            0% {
                transform: scale(0.95) rotate(0deg);
                filter: drop-shadow(0 0 5px rgba(42, 67, 233, 0.2));
            }
            50% {
                transform: scale(1.05) rotate(3deg);
                filter: drop-shadow(0 0 15px rgba(42, 67, 233, 0.5));
            }
            100% {
                transform: scale(0.95) rotate(0deg);
                filter: drop-shadow(0 0 5px rgba(42, 67, 233, 0.2));
            }
        }

        .loading-ring {
            position: absolute;
            width: 160px;
            height: 160px;
            border-radius: 50%;
            border: 3px solid transparent;
            border-top-color: #2a43e9;
            animation: spin 1.5s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="splash_container">
        <div class="loading-ring"></div>
        <img src="/pleros.svg" alt="Pleros logo" class="logo" />
    </div>
</body>
</html>